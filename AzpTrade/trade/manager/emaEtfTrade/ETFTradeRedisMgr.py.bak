"""
ETF交易管理模块的Redis操作
实现ETF交易相关的Redis存取操作，包括：
1. T+0交易状态管理
2. 均线持仓池状态管理
"""

import sys
import os
import time
import json
import threading
import datetime
from functools import wraps

import akshare as ak
import numpy as np
import pandas as pd

import trade.log.YfyLog as yfylog
import trade.manager.TimeDateMgr as timeDateMgr
import trade.network.YFYAccountMgr as yfyam
import trade.redis.StockRedisMgr as redisMgr
from trade.redis.StockRedisMgr import RedisKeyPrefix
from enum import Enum

# 在RedisKeyPrefix中添加均线持仓池相关的键前缀
# 这需要在trade.redis.StockRedisMgr中实现，但这里我们暂时使用字符串常量

# 均线持仓池状态的Redis键前缀
MA_POSITION_STATUS_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':ma:status'
# 均线持仓池最后交易时间的Redis键前缀
MA_LAST_TRADE_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':ma:last_trade'
# 均线持仓池交易记录的Redis键前缀
MA_TRADE_RECORD_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':ma:record'
# T+0交易阈值执行记录的Redis键前缀
T0_THRESHOLD_EXEC_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':t0:threshold_exec'
# T+0尾盘买回执行记录的Redis键前缀
T0_BUYBACK_EXEC_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':t0:buyback_exec'

class T0TradeStatus(Enum):
    """T+0交易状态枚举"""
    NO_TRADE = "no_trade"           # 未交易
    SOLD_FIRST_LEVEL = "sold_first"  # 已卖出第一档
    SOLD_SECOND_LEVEL = "sold_second" # 已卖出第二档
    BOUGHT_BACK = "bought_back"      # 已买回


def trading_time_required(func):
    """
    装饰器：检查当前是否为交易时间，非交易时间不执行Redis写入操作
    
    仅对写入操作函数进行时间检查，读取操作不受影响
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 检查当前是否为交易时间
        is_work_day, is_opening, is_bidding = timeDateMgr.is_open_time()
        
        # 非交易时间跳过写入操作
        if not (is_work_day and (is_opening or is_bidding)):
            yfylog.logger.info(f"当前非交易时间，跳过Redis写入操作: {func.__name__}")
            return False  # 返回False表示操作被跳过
        
        # 交易时间内正常执行函数
        return func(*args, **kwargs)
    
    return wrapper


#---------------------- T+0交易相关操作 ----------------------#

def get_t0_trade_status(symbol, account_type):
    """获取ETF的T+0交易状态
    Args:
        symbol: ETF代码
        account_type: 账户类型
    Returns:
        T0TradeStatus: 交易状态枚举值
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_STATUS.value, symbol, account_type, today_date)
    status = redis_mgr.get_value(key)
    
    if not status:
        return T0TradeStatus.NO_TRADE
    
    try:
        return T0TradeStatus(status)
    except ValueError:
        yfylog.logger.error(f"无效的T+0交易状态: {status}")
        return T0TradeStatus.NO_TRADE

@trading_time_required
def update_t0_trade_status(symbol, account_type, status):
    """更新ETF的T+0交易状态
    Args:
        symbol: ETF代码
        account_type: 账户类型
        status: T0TradeStatus枚举值
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_STATUS.value, symbol, account_type, today_date)
    redis_mgr.set_value(key, status.value, expire=24*60*60)  # 24小时过期
    yfylog.logger.info(f"更新T+0交易状态: {symbol} {account_type} {status.value} 日期: {today_date}")

@trading_time_required
def record_t0_trade_position(symbol, account_type, sold_pct, sold_amount, reason=""):
    """记录T+0交易卖出的持仓信息，用于尾盘买回
    Args:
        symbol: ETF代码
        account_type: 账户类型
        sold_pct: 卖出比例
        sold_amount: 卖出金额
        reason: 交易原因
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_POSITION.value, symbol, account_type, today_date)
    
    # 获取现有记录
    position_data = redis_mgr.get_value(key) or {"total_sold_pct": 0, "total_sold_amount": 0, "trades": [], "date": today_date}
    
    # 更新数据
    position_data["total_sold_pct"] += sold_pct
    position_data["total_sold_amount"] += sold_amount
    position_data["trades"].append({
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "sold_pct": sold_pct,
        "sold_amount": sold_amount,
        "reason": reason
    })
    
    # 保存到Redis
    redis_mgr.set_value(key, position_data, expire=24*60*60)  # 24小时过期
    yfylog.logger.info(f"记录T+0交易持仓: {symbol} {account_type} 卖出比例: {sold_pct:.2f} 卖出金额: {sold_amount} 日期: {today_date}")
    
    # 同时记录交易历史
    record_key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_RECORD.value, symbol, account_type, today_date, int(time.time()))
    trade_record = {
        "symbol": symbol,
        "account_type": account_type,
        "trade_type": "sell",
        "percentage": sold_pct,
        "amount": sold_amount,
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "reason": reason,
        "date": today_date
    }
    redis_mgr.set_value(record_key, trade_record, expire=60*24*60*60)  # 保存60天

def get_t0_trade_position(symbol, account_type):
    """获取T+0交易卖出的持仓信息
    Args:
        symbol: ETF代码
        account_type: 账户类型
    Returns:
        dict: 持仓信息
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(RedisKeyPrefix.TRADE_T0_POSITION.value, symbol, account_type, today_date)
    position_data = redis_mgr.get_value(key) or {"total_sold_pct": 0, "total_sold_amount": 0, "trades": [], "date": today_date}
    return position_data

@trading_time_required
def record_t0_buy_back(symbol, account_type, buyback_amount, reason=""):
    """记录T+0交易的买回操作
    
    Args:
        symbol: ETF代码
        account_type: 账户类型
        buyback_amount: 买回金额
        reason: 买回原因
    """
    try:
        # 获取当前日期
        today = datetime.datetime.now().strftime("%Y%m%d")
        
        # 生成键名
        key = f"{RedisKeyPrefix.TRADE_ETF.value}:t0:buyback:{today}:{symbol}:{account_type}"
        
        # 准备买回数据
        buyback_data = {
            "symbol": symbol,
            "account_type": account_type,
            "buyback_amount": buyback_amount,
            "reason": reason,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 写入Redis
        redis_mgr = redisMgr.StockRedisMgr()
        redis_mgr.set_value(key, buyback_data, expire=60*24*60*60)
        
        yfylog.logger.info(f"记录ETF {symbol} {account_type} T+0买回信息成功，买回金额: {buyback_amount}")
    except Exception as e:
        yfylog.logger.error(f"记录ETF {symbol} {account_type} T+0买回信息失败: {str(e)}")


def has_executed_buyback_today(symbol, account_type, today_date=None):
    """检查今日是否已执行过尾盘买回
    
    Args:
        symbol: ETF代码
        account_type: 账户类型
        today_date: 日期字符串，默认为当天
        
    Returns:
        bool: 是否已执行过买回
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        key = f"{RedisKeyPrefix.TRADE_ETF.value}:{T0_BUYBACK_EXEC_PREFIX}:{today_date}:{symbol}:{account_type}"
        
        # 检查Redis中是否存在该键
        redis_mgr = redisMgr.StockRedisMgr()
        return bool(redis_mgr.exists(key))
    except Exception as e:
        yfylog.logger.error(f"检查ETF {symbol} {account_type} 今日买回状态失败: {str(e)}")
        return False


@trading_time_required
def mark_buyback_executed(symbol, account_type, buyback_amount, today_date=None):
    """标记已执行过尾盘买回
    
    Args:
        symbol: ETF代码
        account_type: 账户类型
        buyback_amount: 买回金额
        today_date: 日期字符串，默认为当天
    """
    try:
        if today_date is None:
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            
        key = f"{RedisKeyPrefix.TRADE_ETF.value}:{T0_BUYBACK_EXEC_PREFIX}:{today_date}:{symbol}:{account_type}"
        
        # 记录执行时间和金额
        buyback_data = {
            "executed_time": datetime.datetime.now().strftime("%H:%M:%S"),
            "buyback_amount": buyback_amount
        }
        
        # 写入Redis并设置当天有效期
        redis_mgr = redisMgr.StockRedisMgr()
        redis_mgr.set_value(key, buyback_data)
        
        # 设置过期时间为当天结束(23:59:59)
        today_end = datetime.datetime.combine(
            datetime.datetime.now().date(), 
            datetime.time(23, 59, 59)
        )
        seconds_until_end = int((today_end - datetime.datetime.now()).total_seconds())
        redis_mgr.expire(key, max(seconds_until_end, 3600))  # 至少保存1小时
        
        yfylog.logger.info(f"标记ETF {symbol} {account_type} 今日已执行尾盘买回")
    except Exception as e:
        yfylog.logger.error(f"标记ETF {symbol} {account_type} 买回状态时发生错误: {str(e)}")


def has_executed_threshold_level(symbol, account_type, threshold_idx, today_date=None):
    """检查某个阈值级别是否已经执行过交易
    Args:
        symbol: ETF代码
        account_type: 账户类型
        threshold_idx: 阈值级别索引（0表示第一档，1表示第二档）
        today_date: 日期字符串，格式为"YYYYMMDD"，默认为当天
    Returns:
        bool: 是否已执行过该阈值级别的交易
    """
    redis_mgr = redisMgr.StockRedisMgr()
    if today_date is None:
        today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(T0_THRESHOLD_EXEC_PREFIX, symbol, account_type, str(threshold_idx), today_date)
    
    # 检查该阈值级别是否已执行过
    result = redis_mgr.get_value(key)
    return bool(result)
    
@trading_time_required
def mark_threshold_level_executed(symbol, account_type, threshold_idx, change_pct, sell_pct, sell_amount, today_date=None):
    """标记某个阈值级别已执行交易
    Args:
        symbol: ETF代码
        account_type: 账户类型
        threshold_idx: 阈值级别索引
        change_pct: 执行时的涨跌幅
        sell_pct: 卖出比例
        sell_amount: 卖出金额
        today_date: 日期字符串，格式为"YYYYMMDD"，默认为当天
    """
    redis_mgr = redisMgr.StockRedisMgr()
    if today_date is None:
        today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(T0_THRESHOLD_EXEC_PREFIX, symbol, account_type, str(threshold_idx), today_date)
    
    # 记录执行信息
    exec_data = {
        "executed": True,
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "change_pct": change_pct,
        "sell_pct": sell_pct,
        "sell_amount": sell_amount
    }
    
    redis_mgr.set_value(key, exec_data, expire=24*60*60)  # 24小时过期
    yfylog.logger.info(f"标记T+0阈值级别已执行: {symbol} {account_type} 第{threshold_idx+1}档 涨跌幅: {change_pct:.2f}% 卖出比例: {sell_pct:.2f} 卖出金额: {sell_amount:.2f}")

def reset_daily_t0_trading_state():
    """重置每日T+0交易状态
    在每个交易日开始时调用此函数，清除前一天的交易记录和状态
    """
    try:
        # 获取昨天的日期
        yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y%m%d")
        
        # 清除昨日交易状态
        status_keys = redisMgr.StockRedisMgr().keys(f"{RedisKeyPrefix.TRADE_ETF.value}:t0:status:{yesterday}:*")
        if status_keys:
            yfylog.logger.info(f"清除昨日T+0交易状态: {len(status_keys)}条")
            for key in status_keys:
                redisMgr.StockRedisMgr().delete_keys(key)
        
        # 清除昨日交易记录
        position_keys = redisMgr.StockRedisMgr().keys(f"{RedisKeyPrefix.TRADE_ETF.value}:t0:position:{yesterday}:*")
        if position_keys:
            yfylog.logger.info(f"清除昨日T+0交易记录: {len(position_keys)}条")
            for key in position_keys:
                redisMgr.StockRedisMgr().delete(key)
                
        # 清除昨日阈值执行记录
        threshold_keys = redisMgr.StockRedisMgr().keys(f"{RedisKeyPrefix.TRADE_ETF.value}:{T0_THRESHOLD_EXEC_PREFIX}:{yesterday}:*")
        if threshold_keys:
            yfylog.logger.info(f"清除昨日T+0阈值执行记录: {len(threshold_keys)}条")
            for key in threshold_keys:
                redisMgr.StockRedisMgr().delete_keys(key)
                
        # 清除昨日买回执行记录
        buyback_keys = redisMgr.StockRedisMgr().keys(f"{RedisKeyPrefix.TRADE_ETF.value}:{T0_BUYBACK_EXEC_PREFIX}:{yesterday}:*")
        if buyback_keys:
            yfylog.logger.info(f"清除昨日T+0买回执行记录: {len(buyback_keys)}条")
            for key in buyback_keys:
                redisMgr.StockRedisMgr().delete_keys(key)
                
        yfylog.logger.info("每日T+0交易状态重置完成")
    except Exception as e:
        yfylog.logger.error(f"重置每日T+0交易状态失败: {str(e)}", exc_info=True)


#---------------------- 均线持仓池相关操作 ----------------------#

def get_ma_position_status(symbol, account_type, ma_name):
    """获取指定均线池的简化持仓状态（重构版）
    
    只关心该均线池是否处于"激活"状态
    
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
    Returns:
        dict: 包含'position'键的字典 (e.g., {'position': True})
    """
    redis_mgr = redisMgr.StockRedisMgr()
    key = f"{MA_POSITION_STATUS_PREFIX}:{symbol}:{account_type}:{ma_name}"
    position_data = redis_mgr.get_value(key)
    
    if not position_data or not isinstance(position_data, dict):
        return {"position": False}
        
    return {"position": position_data.get("position", False)}

@trading_time_required
def update_ma_position_status(symbol, account_type, ma_name, position_data):
    """更新均线池持仓状态（仅在交易时间内）
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
        position_data: 持仓数据
    Returns:
        bool: 是否成功更新
    """
    try:
        redis_mgr = redisMgr.StockRedisMgr()
        key = f"{MA_POSITION_STATUS_PREFIX}:{symbol}:{account_type}:{ma_name}"
        
        # 设置过期时间为60天
        redis_mgr.set_value(key, position_data, expire=60*24*60*60)
        
        # 记录操作时间
        set_last_ma_trade_time(symbol, account_type, ma_name)
        
        yfylog.logger.info(f"更新均线池状态: {symbol} {account_type} {ma_name} position={position_data.get('position', False)}")
        return True
    except Exception as e:
        yfylog.logger.error(f"更新均线池状态失败: {symbol} {account_type} {ma_name} - {str(e)}")
        return False

def update_ma_position_status_force(symbol, account_type, ma_name, position_data):
    """强制更新均线池持仓状态（不受交易时间限制）
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
        position_data: 持仓数据
    Returns:
        bool: 是否成功更新
    """
    try:
        redis_mgr = redisMgr.StockRedisMgr()
        key = f"{MA_POSITION_STATUS_PREFIX}:{symbol}:{account_type}:{ma_name}"
        
        # 设置过期时间为60天
        redis_mgr.set_value(key, position_data, expire=60*24*60*60)
        
        # 记录操作时间（但不使用trading_time_required装饰器）
        try:
            redis_mgr = redisMgr.StockRedisMgr()
            today_date = datetime.datetime.now().strftime("%Y%m%d")
            time_key = f"{MA_LAST_TRADE_PREFIX}:{symbol}:{account_type}:{ma_name}:{today_date}"
            timestamp = time.time()
            redis_mgr.set_value(time_key, timestamp, expire=60*24*60*60)
        except Exception as e:
            yfylog.logger.warning(f"记录均线池交易时间失败: {e}")
        
        yfylog.logger.info(f"强制更新均线池状态: {symbol} {account_type} {ma_name} position={position_data.get('position', False)}")
        return True
    except Exception as e:
        yfylog.logger.error(f"强制更新均线池状态失败: {symbol} {account_type} {ma_name} - {str(e)}")
        return False

def get_last_ma_trade_time(symbol, account_type, ma_name):
    """获取均线池的最后交易时间
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
    Returns:
        float: 最后交易时间的时间戳
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(MA_LAST_TRADE_PREFIX, symbol, account_type, ma_name, today_date)
    last_time = redis_mgr.get_value(key)
    
    return float(last_time) if last_time else 0

@trading_time_required
def set_last_ma_trade_time(symbol, account_type, ma_name, timestamp=None):
    """设置均线池的最后交易时间
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
        timestamp: 时间戳，默认为当前时间
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    key = redis_mgr.build_key(MA_LAST_TRADE_PREFIX, symbol, account_type, ma_name, today_date)
    
    if timestamp is None:
        timestamp = time.time()
        
    redis_mgr.set_value(key, timestamp, expire=60*24*60*60) # 60天过期
    yfylog.logger.info(f"设置 {symbol} {account_type} {ma_name} 最后交易时间: {timestamp}")

def get_all_ma_position_status(symbol, account_type):
    """获取所有均线池的持仓状态（适配简化版）
    
    Args:
        symbol: ETF代码
        account_type: 账户类型
    Returns:
        dict: key为均线名称，value为持仓状态字典
    """
    redis_mgr = redisMgr.StockRedisMgr()
    keys_pattern = f"{MA_POSITION_STATUS_PREFIX}:{symbol}:{account_type}:*"
    keys = redis_mgr.keys(keys_pattern)
    
    all_status = {}
    if not keys:
        return all_status
        
    for key in keys:
        try:
            # 从key中解析出ma_name
            ma_name = key.split(":")[-1]
            status_data = redis_mgr.get_value(key)
            if status_data and isinstance(status_data, dict):
                 # 返回简化的状态
                all_status[ma_name] = {"position": status_data.get("position", False)}
            else:
                all_status[ma_name] = {"position": False}
        except Exception as e:
            yfylog.logger.error(f"解析均线池状态失败: key={key}, error={e}")
            
    return all_status


#----------------------------------------------------------------------------------------------------------------------------
# 均线穿越历史的Redis键前缀
MA_CROSS_HISTORY_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':ma:cross_history'

@trading_time_required
def record_ma_cross_event(symbol, account_type, ma_name, cross_type, price, ma_value):
    """记录均线穿越事件
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
        cross_type: 穿越类型 ('breakthrough'突破, 'breakdown'跌破)
        price: 穿越时的价格
        ma_value: 均线值
    """
    redis_mgr = redisMgr.StockRedisMgr()
    today_date = datetime.datetime.now().strftime("%Y%m%d")
    timestamp = int(time.time())
    
    # 构建键名
    key = redis_mgr.build_key(MA_CROSS_HISTORY_PREFIX, symbol, account_type, ma_name, today_date, str(timestamp))
    
    # 记录穿越事件
    cross_data = {
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "cross_type": cross_type,
        "price": price,
        "ma_value": ma_value,
        "distance_pct": abs(price - ma_value) / price * 100
    }
    
    # 保存到Redis，保留60天
    redis_mgr.set_value(key, cross_data, expire=60*24*60*60)
    yfylog.logger.info(f"记录均线穿越事件: {symbol} {ma_name} {cross_type} 价格:{price:.4f} 均线:{ma_value:.4f}")

def get_ma_cross_history(symbol, account_type, ma_name, days=5):
    """获取最近N天的均线穿越历史
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
        days: 查询天数
    Returns:
        list: 穿越事件列表
    """
    redis_mgr = redisMgr.StockRedisMgr()
    cross_events = []
    
    # 查询最近N天的数据
    for i in range(days):
        date = (datetime.datetime.now() - datetime.timedelta(days=i)).strftime("%Y%m%d")
        pattern = redis_mgr.build_key(MA_CROSS_HISTORY_PREFIX, symbol, account_type, ma_name, date, "*")
        keys = redis_mgr.keys(pattern)
        
        for key in keys:
            event_data = redis_mgr.get_value(key)
            if event_data:
                cross_events.append(event_data)
    
    # 按时间戳排序
    cross_events.sort(key=lambda x: x["timestamp"])
    return cross_events

def is_oscillating_market(symbol, account_type, ma_name, check_days=5, cross_threshold=3):
    """判断是否为震荡市场
    Args:
        symbol: ETF代码
        account_type: 账户类型
        ma_name: 均线名称
        check_days: 检查天数
        cross_threshold: 穿越次数阈值
    Returns:
        tuple: (是否震荡, 穿越次数)
    """
    cross_history = get_ma_cross_history(symbol, account_type, ma_name, days=check_days)
    cross_count = len(cross_history)
    
    is_oscillating = cross_count >= cross_threshold
    
    if is_oscillating:
        yfylog.logger.warning(f"{symbol} {ma_name}均线检测到震荡市场: 最近{check_days}天穿越{cross_count}次")
    
    return is_oscillating, cross_count


#---------------------- 挂单状态记录与重复下单保护 ----------------------#

# 挂单记录的Redis键前缀
PENDING_ORDER_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':pending_order'
# 最近交易记录的Redis键前缀  
RECENT_TRADE_PREFIX = RedisKeyPrefix.TRADE_ETF.value + ':recent_trade'

@trading_time_required
def record_pending_order(symbol, account_type, order_type, quantity, target_pct, reason=""):
    """记录挂单状态，防止重复下单（基于持仓变化检测版本）
    Args:
        symbol: ETF代码
        account_type: 账户类型
        order_type: 订单类型 ('buy' 或 'sell')
        quantity: 订单数量(股票为份数，基金为金额)
        target_pct: 目标仓位比例
        reason: 交易原因
    """
    redis_mgr = redisMgr.StockRedisMgr()
    timestamp = datetime.datetime.now()
    
    # 获取当前持仓状态作为基准
    account_info = yfyam.get_account_datainfo(account_type)
    total_cash = account_info.get("totalCash", 0) if account_info else 0
    stock_info = yfyam.get_account_stock_info(account_type, symbol)
    current_position_gold = stock_info.get("positionGold", 0) if stock_info else 0
    current_position_pct = (current_position_gold / total_cash) if total_cash > 0 else 0
    
    # 构建键名: pending_order:symbol:account_type:order_type
    key = redis_mgr.build_key(PENDING_ORDER_PREFIX, symbol, account_type, order_type)
    
    order_data = {
        "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
        "order_type": order_type, 
        "quantity": quantity,
        "target_pct": target_pct,
        "reason": reason,
        "status": "pending",
        # 【新增】记录挂单时的持仓状态，用于后续成交验证
        "baseline_position_pct": current_position_pct,
        "baseline_position_gold": current_position_gold,
        "expected_direction": "increase" if order_type == "buy" else "decrease"
    }
    
    # 设置10分钟过期，给足时间让订单成交
    redis_mgr.set_value(key, order_data, expire=600)
    yfylog.logger.info(f"记录挂单状态: {symbol} {account_type} {order_type} 数量:{quantity} 目标:{target_pct:.2%} 基准持仓:{current_position_pct:.2%}")

def has_pending_order(symbol, account_type, order_type, target_pct_tolerance=0.1):
    """检查是否存在未完成的挂单（基于持仓变化检测版本）
    Args:
        symbol: ETF代码
        account_type: 账户类型
        order_type: 订单类型 ('buy' 或 'sell')
        target_pct_tolerance: 目标仓位容忍度(百分比)
    Returns:
        tuple: (是否存在挂单, 挂单数据)
    """
    redis_mgr = redisMgr.StockRedisMgr()
    key = redis_mgr.build_key(PENDING_ORDER_PREFIX, symbol, account_type, order_type)
    
    order_data = redis_mgr.get_value(key)
    if not order_data:
        return False, None
        
    # 检查订单是否超时(10分钟内)
    try:
        order_time = datetime.datetime.strptime(order_data["timestamp"], "%Y-%m-%d %H:%M:%S")
        time_diff = (datetime.datetime.now() - order_time).total_seconds()
        
        if time_diff > 600:  # 超过10分钟，认为订单已失效
            yfylog.logger.info(f"挂单超时清理: {symbol} {account_type} {order_type} 超过10分钟")
            clear_pending_order(symbol, account_type, order_type)
            return False, None
            
        # 【新增】通过持仓变化检测订单是否已成交
        baseline_position_pct = order_data.get("baseline_position_pct", 0)
        expected_direction = order_data.get("expected_direction", "")
        
        # 获取当前持仓状态
        account_info = yfyam.get_account_datainfo(account_type)
        if not account_info:
            yfylog.logger.warning(f"无法获取账户信息: {account_type}，保持挂单状态")
            return True, order_data
            
        total_cash = account_info.get("totalCash", 0)
        stock_info = yfyam.get_account_stock_info(account_type, symbol)
        current_position_gold = stock_info.get("positionGold", 0) if stock_info else 0
        current_position_pct = (current_position_gold / total_cash) if total_cash > 0 else 0
        
        # 计算持仓变化
        position_change_pct = current_position_pct - baseline_position_pct
        
        # 检查持仓变化是否符合预期方向
        position_change_threshold = 0.005  # 0.5%的变化阈值
        
        if expected_direction == "increase":
            # 买入订单：预期持仓增加
            if position_change_pct > position_change_threshold:
                yfylog.logger.info(f"检测到买入成交: {symbol} {account_type} 持仓从{baseline_position_pct:.2%}增加到{current_position_pct:.2%}")
                clear_pending_order(symbol, account_type, order_type)
                return False, None
        elif expected_direction == "decrease":
            # 卖出订单：预期持仓减少
            if position_change_pct < -position_change_threshold:
                yfylog.logger.info(f"检测到卖出成交: {symbol} {account_type} 持仓从{baseline_position_pct:.2%}减少到{current_position_pct:.2%}")
                clear_pending_order(symbol, account_type, order_type)
                return False, None
        
        # 持仓未发生预期变化，认为订单仍在挂单中
        yfylog.logger.debug(f"挂单仍在等待成交: {symbol} {account_type} {order_type} 持仓变化:{position_change_pct:.3%} 阈值:±{position_change_threshold:.3%}")
        return True, order_data
        
    except Exception as e:
        yfylog.logger.error(f"挂单持仓检测失败: {e}", exc_info=True)
        # 发生异常时保持挂单状态，避免错误清理
        return True, order_data

def clear_pending_order(symbol, account_type, order_type):
    """清除挂单记录
    Args:
        symbol: ETF代码
        account_type: 账户类型  
        order_type: 订单类型 ('buy' 或 'sell')
    """
    redis_mgr = redisMgr.StockRedisMgr()
    key = redis_mgr.build_key(PENDING_ORDER_PREFIX, symbol, account_type, order_type)
    redis_mgr.delete_keys(key)
    yfylog.logger.debug(f"清除挂单记录: {symbol} {account_type} {order_type}")

@trading_time_required
def record_recent_trade(symbol, account_type, trade_type, quantity, price, target_pct, reason=""):
    """记录最近的交易操作，用于冷却时间检测
    Args:
        symbol: ETF代码
        account_type: 账户类型
        trade_type: 交易类型 ('buy' 或 'sell')
        quantity: 交易数量
        price: 交易价格
        target_pct: 目标仓位比例
        reason: 交易原因
    """
    redis_mgr = redisMgr.StockRedisMgr()
    timestamp = datetime.datetime.now()
    
    # 构建键名: recent_trade:symbol:account_type:timestamp
    key = redis_mgr.build_key(RECENT_TRADE_PREFIX, symbol, account_type, timestamp.strftime("%Y%m%d_%H%M%S"))
    
    trade_data = {
        "timestamp": timestamp.strftime("%Y-%m-%d %H:%M:%S"),
        "trade_type": trade_type,
        "quantity": quantity, 
        "price": price,
        "target_pct": target_pct,
        "reason": reason
    }
    
    # 保留30分钟的交易记录
    redis_mgr.set_value(key, trade_data, expire=1800)
    yfylog.logger.info(f"记录交易操作: {symbol} {account_type} {trade_type} 数量:{quantity} 价格:{price}")

def get_recent_trades(symbol, account_type, minutes=5):
    """获取最近N分钟的交易记录
    Args:
        symbol: ETF代码
        account_type: 账户类型
        minutes: 查询分钟数
    Returns:
        list: 交易记录列表，按时间倒序
    """
    redis_mgr = redisMgr.StockRedisMgr()
    pattern = redis_mgr.build_key(RECENT_TRADE_PREFIX, symbol, account_type, "*")
    keys = redis_mgr.keys(pattern)
    
    trades = []
    cutoff_time = datetime.datetime.now() - datetime.timedelta(minutes=minutes)
    
    for key in keys:
        trade_data = redis_mgr.get_value(key)
        if trade_data:
            try:
                trade_time = datetime.datetime.strptime(trade_data["timestamp"], "%Y-%m-%d %H:%M:%S")
                if trade_time >= cutoff_time:
                    trades.append(trade_data)
            except Exception as e:
                yfylog.logger.error(f"解析交易时间失败: key={key}, error={e}")
    
    # 按时间倒序排列
    trades.sort(key=lambda x: x["timestamp"], reverse=True)
    return trades

def check_trade_cooldown(symbol, account_type, trade_type, cooldown_seconds=60):
    """检查交易冷却时间
    Args:
        symbol: ETF代码
        account_type: 账户类型
        trade_type: 交易类型 ('buy' 或 'sell') 
        cooldown_seconds: 冷却时间(秒)
    Returns:
        tuple: (是否在冷却期, 剩余冷却时间秒数)
    """
    recent_trades = get_recent_trades(symbol, account_type, minutes=2)
    
    for trade in recent_trades:
        if trade["trade_type"] == trade_type:
            try:
                trade_time = datetime.datetime.strptime(trade["timestamp"], "%Y-%m-%d %H:%M:%S")
                time_diff = (datetime.datetime.now() - trade_time).total_seconds()
                
                if time_diff < cooldown_seconds:
                    remaining = cooldown_seconds - time_diff
                    yfylog.logger.warning(f"{symbol} {account_type} {trade_type}操作在冷却期: 剩余{remaining:.0f}秒")
                    return True, remaining
                    
            except Exception as e:
                yfylog.logger.error(f"解析交易冷却时间失败: {e}")
    
    return False, 0

def has_conflicting_recent_trades(symbol, account_type, current_trade_type, minutes=2):
    """检查最近是否有冲突的交易操作(如先卖后买)
    Args:
        symbol: ETF代码
        account_type: 账户类型
        current_trade_type: 当前交易类型 ('buy' 或 'sell')
        minutes: 检查分钟数
    Returns:
        tuple: (是否有冲突, 冲突交易列表)
    """
    recent_trades = get_recent_trades(symbol, account_type, minutes=minutes)
    
    # 定义冲突类型
    conflict_type = "buy" if current_trade_type == "sell" else "sell"
    
    conflicting_trades = [trade for trade in recent_trades if trade["trade_type"] == conflict_type]
    
    if conflicting_trades:
        yfylog.logger.warning(f"{symbol} {account_type}检测到冲突交易: 最近{minutes}分钟内有{len(conflicting_trades)}次{conflict_type}操作")
        return True, conflicting_trades
    
    return False, []
