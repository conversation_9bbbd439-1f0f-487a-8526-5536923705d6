# -*- coding: utf-8 -*-
"""
ETF交易执行模块

负责执行ETF的买入和卖出操作
"""

import sys
import os
import datetime

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

import trade.log.YfyLog as yfylog
import trade.network.YFYAccountMgr as yfyam
import trade.manager.emaEtfTrade.ETFTradeRedisMgr as etfRedisMgr
import trade.manager.emaEtfTrade.ETFTradeT0Mgr as t0Mgr
from trade.manager.emaEtfTrade.ETFTradeRedisMgr import T0TradeStatus
import trade.manager.TimeDateMgr as timeDateMgr
from trade.enum.WxMsgType import WxMsgType
import trade.network.YFYDataMgr as yfydm
import trade.strategy.StrategiesConfig as sConfig

# 旧的交易函数 execute_ma_pool_trade 和 execute_position_adjustment 已被移除。
# 新的核心逻辑见下方的 execute_etf_strategy_trade 函数。

def execute_etf_strategy_trade(symbol: str, account_type: str, active_ma_pools: set, close_price: float, etf_config: dict, reason: str = ""):
    """
    执行基于真实持仓的ETF策略交易（重构后的新核心函数）

    核心逻辑：
    1. 获取真实持仓 (YFYAccountMgr)。
    2. 计算策略目标总仓位 (所有触发的均线池仓位之和)。
    3. 对比真实持仓和目标仓位，计算差额。
    4. 执行买入或卖出以补齐差额。
    5. 更新Redis中的均线池状态（仅记录是否激活）。

    Args:
        symbol (str): ETF代码
        account_type (str): 账户类型
        active_ma_pools (set): 当前触发交易的均线池名称集合 (e.g., {'ma5', 'ema55'})
        close_price (float): 当前价格
        etf_config (dict): 该ETF的完整配置
        reason (str, optional): 交易原因. Defaults to "".

    Returns:
        bool: 交易是否成功执行
    """
    yfylog.logger.info(f"==> 开始执行ETF策略交易: {symbol} 账户:{account_type} 触发均线池:{active_ma_pools} 原因:{reason}")
    
    # 检查是否有未完成的T+0交易，如果有则暂停正常策略调仓
    t0_status = etfRedisMgr.get_t0_trade_status(symbol, account_type)
    if t0_status in [T0TradeStatus.SOLD_FIRST_LEVEL, T0TradeStatus.SOLD_SECOND_LEVEL]:
        yfylog.logger.info(f"{symbol} {account_type} 有未完成的T+0交易(状态:{t0_status.value})，暂停正常策略调仓")
        return False

    is_work_day, is_opening, is_bidding = timeDateMgr.is_open_time()
    # 1. 获取真实状态 (Ground Truth)
    account_info = yfyam.get_account_datainfo(account_type)
    if not account_info:
        yfylog.logger.error(f"获取账户 {account_type} 信息失败，无法执行交易")
        return False

    total_cash = account_info.get("totalCash", 0)
    stock_info = yfyam.get_account_stock_info(account_type, symbol)
    position_gold = stock_info.get("positionGold", 0) if stock_info else 0
    current_real_pct = (position_gold / total_cash) if total_cash > 0 else 0

    yfylog.logger.info(f"账户总资产: {total_cash:.2f}, {symbol}真实持仓市值: {position_gold:.2f}, 真实持仓比例: {current_real_pct:.2%}")

    # 2. 计算策略目标总仓位
    ma_positions_config = etf_config.get("ma_positions", {})
    base_target_pct = sum(ma_positions_config.get(ma_name, 0.0) for ma_name in active_ma_pools)

    # 在策略层应用全局持仓控制因子
    position_pct = sConfig.get_position()
    target_pct = base_target_pct * position_pct

    yfylog.logger.info(f"策略基础目标仓位: {base_target_pct:.2%}, 应用全局因子({position_pct:.2f})后: {target_pct:.2%}")

    # 新增: 获取账户个性化持仓系数
    account_multiplier = 1.0
    try:
        accounts_positions_config = etf_config.get("accounts_positions", {})
        account_multiplier = float(accounts_positions_config.get(account_type, 1.0)) # 默认为1.0

        if account_multiplier != 1.0:
            yfylog.logger.info(f"{symbol} 账户 {account_type} 应用个性化持仓系数 {account_multiplier:.2f}")
    except Exception as e:
        yfylog.logger.error(f"获取账户个性化持仓系数时发生异常: {e}", exc_info=True)


    # 3. 计算并执行差额调整
    # 核心逻辑：系数影响最终目标仓位
    final_target_pct = target_pct * account_multiplier
    adjustment_pct = final_target_pct - current_real_pct
    adjustment_value = total_cash * adjustment_pct
    diff_pct = abs(adjustment_pct)

    yfylog.logger.info(f"持仓调整计算: 均线目标 {base_target_pct:.2%}, 策略目标(全局因子) {target_pct:.2%}, 最终目标(账户个性化) {final_target_pct:.2%}, 真实 {current_real_pct:.2%}, 差额 {adjustment_pct:.2%}, 调整金额 {adjustment_value:.2f}")

    # 仓位差异忽略阈值：当目标仓位和当前仓位差异小于此值时，忽略调仓
    ignore_threshold_pct = etf_config.get("ignore_threshold", 0.0125)  # 默认1.25%

    # 检查是否需要忽略调仓（差异在阈值内）
    if diff_pct <= ignore_threshold_pct:
        yfylog.logger.info(f"仓位忽略: {symbol} 差异{diff_pct:.2%}在忽略阈值{ignore_threshold_pct:.2%}内，跳过调仓")
        # 即使跳过调仓，也需要更新Redis状态
    elif abs(adjustment_value) < 100:
        yfylog.logger.info("调整金额过小(<100)，跳过本次交易")
        # 即使金额小，也需要更新Redis状态
    elif adjustment_value > 0:  # 需要买入
        # 【新增】挂单状态检查和重复下单保护
        has_pending, pending_data = etfRedisMgr.has_pending_order(symbol, account_type, "buy")
        if has_pending:
            yfylog.logger.warning(f"检测到未完成的买入挂单: {symbol} {account_type}, 跳过本次买入操作")
            yfylog.logger.info(f"挂单详情: 时间={pending_data['timestamp']}, 数量={pending_data['quantity']}, 目标={pending_data['target_pct']:.2%}")
            return False
            
        # 检查交易冷却时间（买入冷却60秒）
        in_cooldown, remaining = etfRedisMgr.check_trade_cooldown(symbol, account_type, "buy", cooldown_seconds=60)
        if in_cooldown:
            yfylog.logger.warning(f"买入操作在冷却期: {symbol} {account_type}, 剩余{remaining:.0f}秒")
            return False
            
        # 检查是否有冲突的反向交易（如刚刚卖出又要买入）
        has_conflict, conflicting_trades = etfRedisMgr.has_conflicting_recent_trades(symbol, account_type, "buy", minutes=2)
        if has_conflict:
            latest_sell = conflicting_trades[0]
            yfylog.logger.warning(f"检测到冲突交易: 最近在{latest_sell['timestamp']}执行了卖出操作，可能存在策略冲突")
            # 不直接返回False，但记录警告，让人工审核
        # 检查可用资金是否足够
        available_cash = account_info.get("availableCash", 0)
        if available_cash < adjustment_value:
            msg = f"可用资金不足: {symbol}--{account_type} 需要买入{adjustment_value:.2f}元，可用资金仅{available_cash:.2f}元，跳过买入操作"
            yfylog.logger.warning(msg)
            #yfydm.push_wx_msg(WxMsgType.STRATEGY_BUY,msg)
            #return False
            adjustment_value = max(0, available_cash - 100)
            if adjustment_value < 100:
                yfylog.logger.warning("调整金额过小(<100)，跳过本次交易")
                return False
        
        # 全局仓位控制因子已在策略层应用，此处直接使用计算出的差额
        adjusted_amount = int(adjustment_value)
        
        msg = f"策略交易: {symbol} 增仓至{final_target_pct:.1%}, 触发池:{','.join(active_ma_pools)}"
        yfylog.logger.info(f">>> 执行买入: {msg}, 金额: {adjusted_amount:.2f}, 可用资金: {available_cash:.2f}")
        yfylog.logger.info(f"ETF {symbol} {account_type} 买入金额: {adjusted_amount:.2f} (全局因子已在策略层应用)")
        
        if is_opening and timeDateMgr.debug_str == "":
            account_status = sConfig.get_account_status(account_type)
            if account_status == "close":
                yfylog.logger.warning(f"账户 {account_type} 状态为 {account_status}，禁止买入操作")
                return False
            
            # 【新增】下单前记录挂单状态
            etfRedisMgr.record_pending_order(symbol, account_type, "buy", adjusted_amount, final_target_pct, msg)
            
            is_success = yfyam.buy_stock(account_type=account_type, symbol=symbol, m_amount=adjusted_amount, msg=msg, buy_type=f"Etfs_Strategy_Adjust", cancel_time=600)
            if is_success:
                yfylog.logger.info(f"执行 {symbol} 买入操作成功")
                # 【新增】交易成功后记录交易记录并清除挂单状态
                etfRedisMgr.record_recent_trade(symbol, account_type, "buy", adjusted_amount, close_price, final_target_pct, msg)
                etfRedisMgr.clear_pending_order(symbol, account_type, "buy")
            else:
                yfylog.logger.error(f"执行 {symbol} 买入操作失败")
                # 【新增】交易失败后清除挂单状态
                etfRedisMgr.clear_pending_order(symbol, account_type, "buy")
                return False
        else:
            yfylog.logger.warning(f"执行 {symbol} 买入操作失败: 非交易时间")
            return False
    else:  # 需要卖出
        # 【新增】挂单状态检查和重复下单保护 - 卖出逻辑
        has_pending, pending_data = etfRedisMgr.has_pending_order(symbol, account_type, "sell")
        if has_pending:
            yfylog.logger.warning(f"❌检测到未完成的卖出挂单: {symbol} {account_type}, 跳过本次卖出操作")
            yfylog.logger.info(f"挂单详情: 时间={pending_data['timestamp']}, 数量={pending_data['quantity']}, 目标={pending_data['target_pct']:.2%}")
            return False
        
        # 检查卖出操作冷却时间（卖出冷却90秒，比买入更严格）  
        in_cooldown, remaining = etfRedisMgr.check_trade_cooldown(symbol, account_type, "sell", cooldown_seconds=90)
        if in_cooldown:
            yfylog.logger.warning(f"❌卖出操作在冷却期: {symbol} {account_type}, 剩余{remaining:.0f}秒")
            return False
            
        # 检查是否有冲突的反向交易（如刚刚买入又要卖出）
        has_conflict, conflicting_trades = etfRedisMgr.has_conflicting_recent_trades(symbol, account_type, "sell", minutes=3)
        if has_conflict:
            latest_buy = conflicting_trades[0]
            yfylog.logger.warning(f"⚠️检测到潜在冲突交易: 最近在{latest_buy['timestamp']}执行了买入操作，现在又要卖出")
            yfylog.logger.warning(f"这可能是策略配置变化导致的反向操作，建议人工核查")
            # 对于冲突交易给出更强烈的警告，但不直接阻止（因为可能是合理的策略调整）
            
        # 使用真实持仓金额计算卖出比例
        if position_gold <= 0:
            yfylog.logger.warning("目标卖出，但真实持仓为0，无需操作")
        else:
            # 检查是否在开盘前3分钟(09:30-09:33)，此时间段内不进行卖出操作
            now = datetime.datetime.now()
            if now.hour == 9 and 30 <= now.minute < 33:
                yfylog.logger.info(f"当前时间 {now.strftime('%H:%M:%S')} 为开盘前3分钟，暂停ETF策略卖出操作: {symbol} {account_type}")
                return False

            # 改用精确份数卖出，避免比例计算问题
            sell_amount = abs(adjustment_value)
            available_amount = stock_info.get("availableAmount", 0) if stock_info else 0
            
            if available_amount <= 0:
                yfylog.logger.warning(f"{account_type}: {symbol} 无可用持仓数量，跳过卖出")
                return False
            
            # 计算需要卖出的份数
            target_sell_count = int(sell_amount / close_price) if close_price > 0 else 0
            target_sell_count = (target_sell_count // 100) * 100  # 确保是100的倍数
            
            # 限制在可卖份数范围内
            max_sell_count = (available_amount // 100) * 100  # 可卖份数（100份的倍数）
            actual_sell_count = min(target_sell_count, max_sell_count)
            
            if actual_sell_count >= 100:
                msg = f"策略交易: {symbol} 减仓至{final_target_pct:.1%}, 触发池:{','.join(active_ma_pools)}"
                yfylog.logger.info(f">>> 执行卖出: {msg}, 份数: {actual_sell_count}")
                if is_opening and timeDateMgr.debug_str == "":
                    # 【新增】下单前记录挂单状态 - 卖出
                    etfRedisMgr.record_pending_order(symbol, account_type, "sell", actual_sell_count, final_target_pct, msg)
                    
                    is_success = yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_count=actual_sell_count, msg=msg)
                    if is_success:
                        actual_sell_amount = actual_sell_count * close_price
                        yfylog.logger.info(f"执行 {symbol} 卖出操作成功: {actual_sell_count}份 ({actual_sell_amount:.2f}元)")
                        # 【新增】交易成功后记录交易记录并清除挂单状态 - 卖出
                        etfRedisMgr.record_recent_trade(symbol, account_type, "sell", actual_sell_count, close_price, final_target_pct, msg)
                        etfRedisMgr.clear_pending_order(symbol, account_type, "sell")
                    else:
                        yfylog.logger.error(f"执行 {symbol} 卖出操作失败")
                        # 【新增】交易失败后清除挂单状态 - 卖出
                        etfRedisMgr.clear_pending_order(symbol, account_type, "sell")
                        return False
                else:
                    yfylog.logger.warning(f"执行 {symbol} 卖出操作失败: 非交易时间")
                    return False
            else:
                # 如果计算出的份数不足100份，回退到比例卖出方式
                sell_ratio = abs(adjustment_value) / position_gold
                sell_ratio = min(sell_ratio, 1.0) # 确保不超过100%
                msg = f"策略交易: {symbol} 减仓至{final_target_pct:.1%}, 触发池:{','.join(active_ma_pools)}"
                yfylog.logger.info(f">>> 执行卖出(回退比例): {msg}, 比例: {sell_ratio:.2%}")
                if is_opening and timeDateMgr.debug_str == "":
                    is_success = yfyam.sell_stock(account_type=account_type, symbol=symbol, sell_pct=sell_ratio, msg=msg)
                    if is_success:
                        yfylog.logger.info(f"执行 {symbol} 卖出操作成功")
                    else:
                        yfylog.logger.error(f"执行 {symbol} 卖出操作失败")
                        return False
                else:
                    yfylog.logger.warning(f"执行 {symbol} 卖出操作失败: 非交易时间")
                    return False

    # 4. 更新所有相关均线池在Redis中的状态
    all_configured_pools = ma_positions_config.keys()
    for ma_name in all_configured_pools:
        is_active = ma_name in active_ma_pools
        ma_status = etfRedisMgr.get_ma_position_status(symbol, account_type, ma_name)
        
        # 【关键修复】：每次都更新Redis状态，不仅仅在状态变化时
        current_position = ma_status.get("position", False)
        
        # 更新状态数据
        ma_status["position"] = is_active
        ma_status["position_pct"] = ma_positions_config.get(ma_name, 0.0)
        ma_status["last_update"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 只有在状态真正变化时才更新买入/卖出时间和价格
        if current_position != is_active:
            yfylog.logger.info(f"Redis状态变化: {symbol} {account_type} {ma_name} {current_position} -> {is_active}")
            if is_active:
                ma_status["buy_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ma_status["buy_price"] = close_price
            else: # 从激活变为非激活
                ma_status["sell_time"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                ma_status["sell_price"] = close_price
        else:
            yfylog.logger.info(f"Redis状态保持: {symbol} {account_type} {ma_name} 保持 {is_active} 状态")
        
        # 【关键修复】：每次都强制更新Redis状态，确保last_update时间被更新
        success = etfRedisMgr.update_ma_position_status_force(symbol, account_type, ma_name, ma_status)
        if not success:
            yfylog.logger.error(f"Redis状态更新失败: {symbol} {account_type} {ma_name}")
        else:
            yfylog.logger.debug(f"Redis状态更新成功: {symbol} {account_type} {ma_name} position={is_active}")
    
    yfylog.logger.info(f"<== ETF策略交易执行完毕: {symbol} 账户:{account_type}")
    return True 