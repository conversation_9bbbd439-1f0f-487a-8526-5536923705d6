import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import socket
import trade.log.YfyLog as yfylog

url = 'https://stock.haidian666.com/stock/'
w_ip = '*************'

has_check = False
is_intranet = False
base_ip = '127.0.0.1'


def is_internal_network():
    global url, has_check, is_intranet, base_ip
    """检查是否为内网环境，依次尝试localhost、***********、外网"""
    
    # 定义内网服务器列表，按优先级排序
    internal_servers = [
        ('localhost', 'http://localhost:9020/stock/', '本地localhost'),
        ('***********', 'http://***********:9020/stock/', '内网服务器***********')
    ]
    
    # 依次尝试内网服务器
    for host, base_url, description in internal_servers:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)  # 设置超时时间为1秒
            result = sock.connect_ex((host, 9020))
            sock.close()
            
            if result == 0:
                # 连接成功，使用该内网服务器
                base_ip = host
                url = base_url
                has_check = True
                is_intranet = True
                yfylog.logger.info(f">>>>>>>>>>>>>>>>>>>>>>>>>当前为内网环境，连接的是{description}~<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
                return True
                
        except Exception as e:
            yfylog.logger.debug(f"尝试连接{description}失败: {e}")
            continue
    
    # 所有内网服务器都连接失败，使用外网
    has_check = True
    is_intranet = False
    yfylog.logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>所有内网服务器连接失败，当前为外网环境，连接的是外网域名~<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
    return False


def get_base_url():
    global url, has_check
    if has_check:
        return url
    is_internal_network()
    return url

def get_host():
    global base_ip, has_check
    if has_check:
        return base_ip
    is_internal_network()
    return base_ip


is_internal_network()

def init_proxy():
    import asyncio
    from trade.proxy.aiohttp_proxy import init_proxy, patch_aiohttp

    async def setup():
        # 初始化代理
        init_proxy(
            secret_id="o8n9s1kfucnizldaax48", 
            secret_key="yz5c8ggbgzwn4kspzbicgiyr9lahqc4f",
            min_ips=3,
            max_ips=5,
            expire_hours=2.0,  # 设置为您的IP实际有效期
            auto_refresh=True
        )
        # 全局应用到 aiohttp
        await patch_aiohttp()

    # 运行初始化
    asyncio.run(setup())

#init_proxy()    