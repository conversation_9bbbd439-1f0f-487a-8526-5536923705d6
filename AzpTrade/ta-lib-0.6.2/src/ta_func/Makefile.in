# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@


VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = src/ta_func
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(libta_func_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
LTLIBRARIES = $(noinst_LTLIBRARIES)
libta_func_la_LIBADD =
am_libta_func_la_OBJECTS = ta_utility.lo ta_ACCBANDS.lo ta_ACOS.lo \
	ta_AD.lo ta_ADD.lo ta_ADOSC.lo ta_ADX.lo ta_ADXR.lo ta_APO.lo \
	ta_AROON.lo ta_AROONOSC.lo ta_ASIN.lo ta_ATAN.lo ta_ATR.lo \
	ta_AVGPRICE.lo ta_AVGDEV.lo ta_BBANDS.lo ta_BETA.lo ta_BOP.lo \
	ta_CCI.lo ta_CDL2CROWS.lo ta_CDL3BLACKCROWS.lo \
	ta_CDL3INSIDE.lo ta_CDL3LINESTRIKE.lo ta_CDL3OUTSIDE.lo \
	ta_CDL3STARSINSOUTH.lo ta_CDL3WHITESOLDIERS.lo \
	ta_CDLABANDONEDBABY.lo ta_CDLADVANCEBLOCK.lo ta_CDLBELTHOLD.lo \
	ta_CDLBREAKAWAY.lo ta_CDLCLOSINGMARUBOZU.lo \
	ta_CDLCONCEALBABYSWALL.lo ta_CDLCOUNTERATTACK.lo \
	ta_CDLDARKCLOUDCOVER.lo ta_CDLDOJI.lo ta_CDLDOJISTAR.lo \
	ta_CDLDRAGONFLYDOJI.lo ta_CDLENGULFING.lo \
	ta_CDLEVENINGDOJISTAR.lo ta_CDLEVENINGSTAR.lo \
	ta_CDLGAPSIDESIDEWHITE.lo ta_CDLGRAVESTONEDOJI.lo \
	ta_CDLHAMMER.lo ta_CDLHANGINGMAN.lo ta_CDLHARAMI.lo \
	ta_CDLHARAMICROSS.lo ta_CDLHIGHWAVE.lo ta_CDLHIKKAKE.lo \
	ta_CDLHIKKAKEMOD.lo ta_CDLHOMINGPIGEON.lo \
	ta_CDLIDENTICAL3CROWS.lo ta_CDLINNECK.lo \
	ta_CDLINVERTEDHAMMER.lo ta_CDLKICKING.lo \
	ta_CDLKICKINGBYLENGTH.lo ta_CDLLADDERBOTTOM.lo \
	ta_CDLLONGLEGGEDDOJI.lo ta_CDLLONGLINE.lo ta_CDLMARUBOZU.lo \
	ta_CDLMATCHINGLOW.lo ta_CDLMATHOLD.lo ta_CDLMORNINGDOJISTAR.lo \
	ta_CDLMORNINGSTAR.lo ta_CDLONNECK.lo ta_CDLPIERCING.lo \
	ta_CDLRICKSHAWMAN.lo ta_CDLRISEFALL3METHODS.lo \
	ta_CDLSEPARATINGLINES.lo ta_CDLSHOOTINGSTAR.lo \
	ta_CDLSHORTLINE.lo ta_CDLSPINNINGTOP.lo \
	ta_CDLSTALLEDPATTERN.lo ta_CDLSTICKSANDWICH.lo ta_CDLTAKURI.lo \
	ta_CDLTASUKIGAP.lo ta_CDLTHRUSTING.lo ta_CDLTRISTAR.lo \
	ta_CDLUNIQUE3RIVER.lo ta_CDLUPSIDEGAP2CROWS.lo \
	ta_CDLXSIDEGAP3METHODS.lo ta_CEIL.lo ta_CMO.lo ta_CORREL.lo \
	ta_COS.lo ta_COSH.lo ta_DEMA.lo ta_DIV.lo ta_DX.lo ta_EMA.lo \
	ta_EXP.lo ta_FLOOR.lo ta_HT_DCPERIOD.lo ta_HT_DCPHASE.lo \
	ta_HT_PHASOR.lo ta_HT_SINE.lo ta_HT_TRENDLINE.lo \
	ta_HT_TRENDMODE.lo ta_IMI.lo ta_KAMA.lo ta_LINEARREG.lo \
	ta_LINEARREG_ANGLE.lo ta_LINEARREG_INTERCEPT.lo \
	ta_LINEARREG_SLOPE.lo ta_LN.lo ta_LOG10.lo ta_MA.lo ta_MACD.lo \
	ta_MACDEXT.lo ta_MACDFIX.lo ta_MAMA.lo ta_MAVP.lo ta_MAX.lo \
	ta_MAXINDEX.lo ta_MEDPRICE.lo ta_MFI.lo ta_MIDPOINT.lo \
	ta_MIDPRICE.lo ta_MIN.lo ta_MININDEX.lo ta_MINMAX.lo \
	ta_MINMAXINDEX.lo ta_MINUS_DI.lo ta_MINUS_DM.lo ta_MOM.lo \
	ta_MULT.lo ta_NATR.lo ta_OBV.lo ta_PLUS_DI.lo ta_PLUS_DM.lo \
	ta_PPO.lo ta_ROC.lo ta_ROCP.lo ta_ROCR.lo ta_ROCR100.lo \
	ta_RSI.lo ta_SAR.lo ta_SAREXT.lo ta_SIN.lo ta_SINH.lo \
	ta_SMA.lo ta_SQRT.lo ta_STDDEV.lo ta_STOCH.lo ta_STOCHF.lo \
	ta_STOCHRSI.lo ta_SUB.lo ta_SUM.lo ta_T3.lo ta_TAN.lo \
	ta_TANH.lo ta_TEMA.lo ta_TRANGE.lo ta_TRIMA.lo ta_TRIX.lo \
	ta_TSF.lo ta_TYPPRICE.lo ta_ULTOSC.lo ta_VAR.lo ta_WCLPRICE.lo \
	ta_WILLR.lo ta_WMA.lo
libta_func_la_OBJECTS = $(am_libta_func_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)/include
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/ta_ACCBANDS.Plo \
	./$(DEPDIR)/ta_ACOS.Plo ./$(DEPDIR)/ta_AD.Plo \
	./$(DEPDIR)/ta_ADD.Plo ./$(DEPDIR)/ta_ADOSC.Plo \
	./$(DEPDIR)/ta_ADX.Plo ./$(DEPDIR)/ta_ADXR.Plo \
	./$(DEPDIR)/ta_APO.Plo ./$(DEPDIR)/ta_AROON.Plo \
	./$(DEPDIR)/ta_AROONOSC.Plo ./$(DEPDIR)/ta_ASIN.Plo \
	./$(DEPDIR)/ta_ATAN.Plo ./$(DEPDIR)/ta_ATR.Plo \
	./$(DEPDIR)/ta_AVGDEV.Plo ./$(DEPDIR)/ta_AVGPRICE.Plo \
	./$(DEPDIR)/ta_BBANDS.Plo ./$(DEPDIR)/ta_BETA.Plo \
	./$(DEPDIR)/ta_BOP.Plo ./$(DEPDIR)/ta_CCI.Plo \
	./$(DEPDIR)/ta_CDL2CROWS.Plo ./$(DEPDIR)/ta_CDL3BLACKCROWS.Plo \
	./$(DEPDIR)/ta_CDL3INSIDE.Plo \
	./$(DEPDIR)/ta_CDL3LINESTRIKE.Plo \
	./$(DEPDIR)/ta_CDL3OUTSIDE.Plo \
	./$(DEPDIR)/ta_CDL3STARSINSOUTH.Plo \
	./$(DEPDIR)/ta_CDL3WHITESOLDIERS.Plo \
	./$(DEPDIR)/ta_CDLABANDONEDBABY.Plo \
	./$(DEPDIR)/ta_CDLADVANCEBLOCK.Plo \
	./$(DEPDIR)/ta_CDLBELTHOLD.Plo ./$(DEPDIR)/ta_CDLBREAKAWAY.Plo \
	./$(DEPDIR)/ta_CDLCLOSINGMARUBOZU.Plo \
	./$(DEPDIR)/ta_CDLCONCEALBABYSWALL.Plo \
	./$(DEPDIR)/ta_CDLCOUNTERATTACK.Plo \
	./$(DEPDIR)/ta_CDLDARKCLOUDCOVER.Plo \
	./$(DEPDIR)/ta_CDLDOJI.Plo ./$(DEPDIR)/ta_CDLDOJISTAR.Plo \
	./$(DEPDIR)/ta_CDLDRAGONFLYDOJI.Plo \
	./$(DEPDIR)/ta_CDLENGULFING.Plo \
	./$(DEPDIR)/ta_CDLEVENINGDOJISTAR.Plo \
	./$(DEPDIR)/ta_CDLEVENINGSTAR.Plo \
	./$(DEPDIR)/ta_CDLGAPSIDESIDEWHITE.Plo \
	./$(DEPDIR)/ta_CDLGRAVESTONEDOJI.Plo \
	./$(DEPDIR)/ta_CDLHAMMER.Plo ./$(DEPDIR)/ta_CDLHANGINGMAN.Plo \
	./$(DEPDIR)/ta_CDLHARAMI.Plo ./$(DEPDIR)/ta_CDLHARAMICROSS.Plo \
	./$(DEPDIR)/ta_CDLHIGHWAVE.Plo ./$(DEPDIR)/ta_CDLHIKKAKE.Plo \
	./$(DEPDIR)/ta_CDLHIKKAKEMOD.Plo \
	./$(DEPDIR)/ta_CDLHOMINGPIGEON.Plo \
	./$(DEPDIR)/ta_CDLIDENTICAL3CROWS.Plo \
	./$(DEPDIR)/ta_CDLINNECK.Plo \
	./$(DEPDIR)/ta_CDLINVERTEDHAMMER.Plo \
	./$(DEPDIR)/ta_CDLKICKING.Plo \
	./$(DEPDIR)/ta_CDLKICKINGBYLENGTH.Plo \
	./$(DEPDIR)/ta_CDLLADDERBOTTOM.Plo \
	./$(DEPDIR)/ta_CDLLONGLEGGEDDOJI.Plo \
	./$(DEPDIR)/ta_CDLLONGLINE.Plo ./$(DEPDIR)/ta_CDLMARUBOZU.Plo \
	./$(DEPDIR)/ta_CDLMATCHINGLOW.Plo \
	./$(DEPDIR)/ta_CDLMATHOLD.Plo \
	./$(DEPDIR)/ta_CDLMORNINGDOJISTAR.Plo \
	./$(DEPDIR)/ta_CDLMORNINGSTAR.Plo ./$(DEPDIR)/ta_CDLONNECK.Plo \
	./$(DEPDIR)/ta_CDLPIERCING.Plo \
	./$(DEPDIR)/ta_CDLRICKSHAWMAN.Plo \
	./$(DEPDIR)/ta_CDLRISEFALL3METHODS.Plo \
	./$(DEPDIR)/ta_CDLSEPARATINGLINES.Plo \
	./$(DEPDIR)/ta_CDLSHOOTINGSTAR.Plo \
	./$(DEPDIR)/ta_CDLSHORTLINE.Plo \
	./$(DEPDIR)/ta_CDLSPINNINGTOP.Plo \
	./$(DEPDIR)/ta_CDLSTALLEDPATTERN.Plo \
	./$(DEPDIR)/ta_CDLSTICKSANDWICH.Plo \
	./$(DEPDIR)/ta_CDLTAKURI.Plo ./$(DEPDIR)/ta_CDLTASUKIGAP.Plo \
	./$(DEPDIR)/ta_CDLTHRUSTING.Plo ./$(DEPDIR)/ta_CDLTRISTAR.Plo \
	./$(DEPDIR)/ta_CDLUNIQUE3RIVER.Plo \
	./$(DEPDIR)/ta_CDLUPSIDEGAP2CROWS.Plo \
	./$(DEPDIR)/ta_CDLXSIDEGAP3METHODS.Plo ./$(DEPDIR)/ta_CEIL.Plo \
	./$(DEPDIR)/ta_CMO.Plo ./$(DEPDIR)/ta_CORREL.Plo \
	./$(DEPDIR)/ta_COS.Plo ./$(DEPDIR)/ta_COSH.Plo \
	./$(DEPDIR)/ta_DEMA.Plo ./$(DEPDIR)/ta_DIV.Plo \
	./$(DEPDIR)/ta_DX.Plo ./$(DEPDIR)/ta_EMA.Plo \
	./$(DEPDIR)/ta_EXP.Plo ./$(DEPDIR)/ta_FLOOR.Plo \
	./$(DEPDIR)/ta_HT_DCPERIOD.Plo ./$(DEPDIR)/ta_HT_DCPHASE.Plo \
	./$(DEPDIR)/ta_HT_PHASOR.Plo ./$(DEPDIR)/ta_HT_SINE.Plo \
	./$(DEPDIR)/ta_HT_TRENDLINE.Plo \
	./$(DEPDIR)/ta_HT_TRENDMODE.Plo ./$(DEPDIR)/ta_IMI.Plo \
	./$(DEPDIR)/ta_KAMA.Plo ./$(DEPDIR)/ta_LINEARREG.Plo \
	./$(DEPDIR)/ta_LINEARREG_ANGLE.Plo \
	./$(DEPDIR)/ta_LINEARREG_INTERCEPT.Plo \
	./$(DEPDIR)/ta_LINEARREG_SLOPE.Plo ./$(DEPDIR)/ta_LN.Plo \
	./$(DEPDIR)/ta_LOG10.Plo ./$(DEPDIR)/ta_MA.Plo \
	./$(DEPDIR)/ta_MACD.Plo ./$(DEPDIR)/ta_MACDEXT.Plo \
	./$(DEPDIR)/ta_MACDFIX.Plo ./$(DEPDIR)/ta_MAMA.Plo \
	./$(DEPDIR)/ta_MAVP.Plo ./$(DEPDIR)/ta_MAX.Plo \
	./$(DEPDIR)/ta_MAXINDEX.Plo ./$(DEPDIR)/ta_MEDPRICE.Plo \
	./$(DEPDIR)/ta_MFI.Plo ./$(DEPDIR)/ta_MIDPOINT.Plo \
	./$(DEPDIR)/ta_MIDPRICE.Plo ./$(DEPDIR)/ta_MIN.Plo \
	./$(DEPDIR)/ta_MININDEX.Plo ./$(DEPDIR)/ta_MINMAX.Plo \
	./$(DEPDIR)/ta_MINMAXINDEX.Plo ./$(DEPDIR)/ta_MINUS_DI.Plo \
	./$(DEPDIR)/ta_MINUS_DM.Plo ./$(DEPDIR)/ta_MOM.Plo \
	./$(DEPDIR)/ta_MULT.Plo ./$(DEPDIR)/ta_NATR.Plo \
	./$(DEPDIR)/ta_OBV.Plo ./$(DEPDIR)/ta_PLUS_DI.Plo \
	./$(DEPDIR)/ta_PLUS_DM.Plo ./$(DEPDIR)/ta_PPO.Plo \
	./$(DEPDIR)/ta_ROC.Plo ./$(DEPDIR)/ta_ROCP.Plo \
	./$(DEPDIR)/ta_ROCR.Plo ./$(DEPDIR)/ta_ROCR100.Plo \
	./$(DEPDIR)/ta_RSI.Plo ./$(DEPDIR)/ta_SAR.Plo \
	./$(DEPDIR)/ta_SAREXT.Plo ./$(DEPDIR)/ta_SIN.Plo \
	./$(DEPDIR)/ta_SINH.Plo ./$(DEPDIR)/ta_SMA.Plo \
	./$(DEPDIR)/ta_SQRT.Plo ./$(DEPDIR)/ta_STDDEV.Plo \
	./$(DEPDIR)/ta_STOCH.Plo ./$(DEPDIR)/ta_STOCHF.Plo \
	./$(DEPDIR)/ta_STOCHRSI.Plo ./$(DEPDIR)/ta_SUB.Plo \
	./$(DEPDIR)/ta_SUM.Plo ./$(DEPDIR)/ta_T3.Plo \
	./$(DEPDIR)/ta_TAN.Plo ./$(DEPDIR)/ta_TANH.Plo \
	./$(DEPDIR)/ta_TEMA.Plo ./$(DEPDIR)/ta_TRANGE.Plo \
	./$(DEPDIR)/ta_TRIMA.Plo ./$(DEPDIR)/ta_TRIX.Plo \
	./$(DEPDIR)/ta_TSF.Plo ./$(DEPDIR)/ta_TYPPRICE.Plo \
	./$(DEPDIR)/ta_ULTOSC.Plo ./$(DEPDIR)/ta_VAR.Plo \
	./$(DEPDIR)/ta_WCLPRICE.Plo ./$(DEPDIR)/ta_WILLR.Plo \
	./$(DEPDIR)/ta_WMA.Plo ./$(DEPDIR)/ta_utility.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libta_func_la_SOURCES)
DIST_SOURCES = $(libta_func_la_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libta_funcdir)"
HEADERS = $(libta_func_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBM = @LIBM@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
POW_LIB = @POW_LIB@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TALIB_LIBRARY_VERSION = @TALIB_LIBRARY_VERSION@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
noinst_LTLIBRARIES = libta_func.la
AM_CPPFLAGS = -I../ta_common/
libta_func_la_SOURCES = ta_utility.c \
	ta_ACCBANDS.c \
	ta_ACOS.c \
	ta_AD.c \
	ta_ADD.c \
	ta_ADOSC.c \
	ta_ADX.c \
	ta_ADXR.c \
	ta_APO.c \
	ta_AROON.c \
	ta_AROONOSC.c \
	ta_ASIN.c \
	ta_ATAN.c \
	ta_ATR.c \
	ta_AVGPRICE.c \
	ta_AVGDEV.c \
	ta_BBANDS.c \
	ta_BETA.c \
	ta_BOP.c \
	ta_CCI.c \
	ta_CDL2CROWS.c \
	ta_CDL3BLACKCROWS.c \
	ta_CDL3INSIDE.c \
	ta_CDL3LINESTRIKE.c \
	ta_CDL3OUTSIDE.c \
	ta_CDL3STARSINSOUTH.c \
	ta_CDL3WHITESOLDIERS.c \
	ta_CDLABANDONEDBABY.c \
	ta_CDLADVANCEBLOCK.c \
	ta_CDLBELTHOLD.c \
	ta_CDLBREAKAWAY.c \
	ta_CDLCLOSINGMARUBOZU.c \
	ta_CDLCONCEALBABYSWALL.c \
	ta_CDLCOUNTERATTACK.c \
	ta_CDLDARKCLOUDCOVER.c \
	ta_CDLDOJI.c \
	ta_CDLDOJISTAR.c \
	ta_CDLDRAGONFLYDOJI.c \
	ta_CDLENGULFING.c \
	ta_CDLEVENINGDOJISTAR.c \
	ta_CDLEVENINGSTAR.c \
	ta_CDLGAPSIDESIDEWHITE.c \
	ta_CDLGRAVESTONEDOJI.c \
	ta_CDLHAMMER.c \
	ta_CDLHANGINGMAN.c \
	ta_CDLHARAMI.c \
	ta_CDLHARAMICROSS.c \
	ta_CDLHIGHWAVE.c \
	ta_CDLHIKKAKE.c \
	ta_CDLHIKKAKEMOD.c \
	ta_CDLHOMINGPIGEON.c \
	ta_CDLIDENTICAL3CROWS.c \
	ta_CDLINNECK.c \
	ta_CDLINVERTEDHAMMER.c \
	ta_CDLKICKING.c \
	ta_CDLKICKINGBYLENGTH.c \
	ta_CDLLADDERBOTTOM.c \
	ta_CDLLONGLEGGEDDOJI.c \
	ta_CDLLONGLINE.c \
	ta_CDLMARUBOZU.c \
	ta_CDLMATCHINGLOW.c \
	ta_CDLMATHOLD.c \
	ta_CDLMORNINGDOJISTAR.c \
	ta_CDLMORNINGSTAR.c \
	ta_CDLONNECK.c \
	ta_CDLPIERCING.c \
	ta_CDLRICKSHAWMAN.c \
	ta_CDLRISEFALL3METHODS.c \
	ta_CDLSEPARATINGLINES.c \
	ta_CDLSHOOTINGSTAR.c \
	ta_CDLSHORTLINE.c \
	ta_CDLSPINNINGTOP.c \
	ta_CDLSTALLEDPATTERN.c \
	ta_CDLSTICKSANDWICH.c \
	ta_CDLTAKURI.c \
	ta_CDLTASUKIGAP.c \
	ta_CDLTHRUSTING.c \
	ta_CDLTRISTAR.c \
	ta_CDLUNIQUE3RIVER.c \
	ta_CDLUPSIDEGAP2CROWS.c \
	ta_CDLXSIDEGAP3METHODS.c \
	ta_CEIL.c \
	ta_CMO.c \
	ta_CORREL.c \
	ta_COS.c \
	ta_COSH.c \
	ta_DEMA.c \
	ta_DIV.c \
	ta_DX.c \
	ta_EMA.c \
	ta_EXP.c \
	ta_FLOOR.c \
	ta_HT_DCPERIOD.c \
	ta_HT_DCPHASE.c \
	ta_HT_PHASOR.c \
	ta_HT_SINE.c \
	ta_HT_TRENDLINE.c \
	ta_HT_TRENDMODE.c \
	ta_IMI.c \
	ta_KAMA.c \
	ta_LINEARREG.c \
	ta_LINEARREG_ANGLE.c \
	ta_LINEARREG_INTERCEPT.c \
	ta_LINEARREG_SLOPE.c \
	ta_LN.c \
	ta_LOG10.c \
	ta_MA.c \
	ta_MACD.c \
	ta_MACDEXT.c \
	ta_MACDFIX.c \
	ta_MAMA.c \
	ta_MAVP.c \
	ta_MAX.c \
	ta_MAXINDEX.c \
	ta_MEDPRICE.c \
	ta_MFI.c \
	ta_MIDPOINT.c \
	ta_MIDPRICE.c \
	ta_MIN.c \
	ta_MININDEX.c \
	ta_MINMAX.c \
	ta_MINMAXINDEX.c \
	ta_MINUS_DI.c \
	ta_MINUS_DM.c \
	ta_MOM.c \
	ta_MULT.c \
	ta_NATR.c \
	ta_OBV.c \
	ta_PLUS_DI.c \
	ta_PLUS_DM.c \
	ta_PPO.c \
	ta_ROC.c \
	ta_ROCP.c \
	ta_ROCR.c \
	ta_ROCR100.c \
	ta_RSI.c \
	ta_SAR.c \
	ta_SAREXT.c \
	ta_SIN.c \
	ta_SINH.c \
	ta_SMA.c \
	ta_SQRT.c \
	ta_STDDEV.c \
	ta_STOCH.c \
	ta_STOCHF.c \
	ta_STOCHRSI.c \
	ta_SUB.c \
	ta_SUM.c \
	ta_T3.c \
	ta_TAN.c \
	ta_TANH.c \
	ta_TEMA.c \
	ta_TRANGE.c \
	ta_TRIMA.c \
	ta_TRIX.c \
	ta_TSF.c \
	ta_TYPPRICE.c \
	ta_ULTOSC.c \
	ta_VAR.c \
	ta_WCLPRICE.c \
	ta_WILLR.c \
	ta_WMA.c

libta_funcdir = $(includedir)/ta-lib/
libta_func_HEADERS = ../../include/ta_defs.h \
	../../include/ta_libc.h \
	../../include/ta_func.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/ta_func/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/ta_func/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

libta_func.la: $(libta_func_la_OBJECTS) $(libta_func_la_DEPENDENCIES) $(EXTRA_libta_func_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libta_func_la_OBJECTS) $(libta_func_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ACCBANDS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ACOS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADOSC.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ADXR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_APO.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AROON.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AROONOSC.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ASIN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ATAN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ATR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AVGDEV.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_AVGPRICE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_BBANDS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_BETA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_BOP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CCI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL2CROWS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3BLACKCROWS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3INSIDE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3LINESTRIKE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3OUTSIDE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3STARSINSOUTH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDL3WHITESOLDIERS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLABANDONEDBABY.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLADVANCEBLOCK.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLBELTHOLD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLBREAKAWAY.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLCLOSINGMARUBOZU.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLCONCEALBABYSWALL.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLCOUNTERATTACK.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDARKCLOUDCOVER.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDOJI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDOJISTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLDRAGONFLYDOJI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLENGULFING.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLEVENINGDOJISTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLEVENINGSTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLGAPSIDESIDEWHITE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLGRAVESTONEDOJI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHAMMER.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHANGINGMAN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHARAMI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHARAMICROSS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHIGHWAVE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHIKKAKE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHIKKAKEMOD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLHOMINGPIGEON.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLIDENTICAL3CROWS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLINNECK.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLINVERTEDHAMMER.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLKICKING.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLKICKINGBYLENGTH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLLADDERBOTTOM.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLLONGLEGGEDDOJI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLLONGLINE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMARUBOZU.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMATCHINGLOW.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMATHOLD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMORNINGDOJISTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLMORNINGSTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLONNECK.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLPIERCING.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLRICKSHAWMAN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLRISEFALL3METHODS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSEPARATINGLINES.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSHOOTINGSTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSHORTLINE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSPINNINGTOP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSTALLEDPATTERN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLSTICKSANDWICH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTAKURI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTASUKIGAP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTHRUSTING.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLTRISTAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLUNIQUE3RIVER.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLUPSIDEGAP2CROWS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CDLXSIDEGAP3METHODS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CEIL.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CMO.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_CORREL.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_COS.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_COSH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_DEMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_DIV.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_DX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_EMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_EXP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_FLOOR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_DCPERIOD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_DCPHASE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_PHASOR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_SINE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_TRENDLINE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_HT_TRENDMODE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_IMI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_KAMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG_ANGLE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG_INTERCEPT.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LINEARREG_SLOPE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_LOG10.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MACD.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MACDEXT.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MACDFIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAVP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MAXINDEX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MEDPRICE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MFI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MIDPOINT.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MIDPRICE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MIN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MININDEX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINMAX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINMAXINDEX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINUS_DI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MINUS_DM.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MOM.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_MULT.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_NATR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_OBV.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_PLUS_DI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_PLUS_DM.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_PPO.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROC.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROCP.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROCR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ROCR100.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_RSI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SAREXT.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SIN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SINH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SQRT.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STDDEV.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STOCH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STOCHF.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_STOCHRSI.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SUB.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_SUM.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_T3.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TAN.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TANH.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TEMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TRANGE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TRIMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TRIX.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TSF.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_TYPPRICE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_ULTOSC.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_VAR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_WCLPRICE.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_WILLR.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_WMA.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_utility.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-libta_funcHEADERS: $(libta_func_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(libta_func_HEADERS)'; test -n "$(libta_funcdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libta_funcdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libta_funcdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(libta_funcdir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(libta_funcdir)" || exit $$?; \
	done

uninstall-libta_funcHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(libta_func_HEADERS)'; test -n "$(libta_funcdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(libta_funcdir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libta_funcdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/ta_ACCBANDS.Plo
	-rm -f ./$(DEPDIR)/ta_ACOS.Plo
	-rm -f ./$(DEPDIR)/ta_AD.Plo
	-rm -f ./$(DEPDIR)/ta_ADD.Plo
	-rm -f ./$(DEPDIR)/ta_ADOSC.Plo
	-rm -f ./$(DEPDIR)/ta_ADX.Plo
	-rm -f ./$(DEPDIR)/ta_ADXR.Plo
	-rm -f ./$(DEPDIR)/ta_APO.Plo
	-rm -f ./$(DEPDIR)/ta_AROON.Plo
	-rm -f ./$(DEPDIR)/ta_AROONOSC.Plo
	-rm -f ./$(DEPDIR)/ta_ASIN.Plo
	-rm -f ./$(DEPDIR)/ta_ATAN.Plo
	-rm -f ./$(DEPDIR)/ta_ATR.Plo
	-rm -f ./$(DEPDIR)/ta_AVGDEV.Plo
	-rm -f ./$(DEPDIR)/ta_AVGPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_BBANDS.Plo
	-rm -f ./$(DEPDIR)/ta_BETA.Plo
	-rm -f ./$(DEPDIR)/ta_BOP.Plo
	-rm -f ./$(DEPDIR)/ta_CCI.Plo
	-rm -f ./$(DEPDIR)/ta_CDL2CROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3BLACKCROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3INSIDE.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3LINESTRIKE.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3OUTSIDE.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3STARSINSOUTH.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3WHITESOLDIERS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLABANDONEDBABY.Plo
	-rm -f ./$(DEPDIR)/ta_CDLADVANCEBLOCK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLBELTHOLD.Plo
	-rm -f ./$(DEPDIR)/ta_CDLBREAKAWAY.Plo
	-rm -f ./$(DEPDIR)/ta_CDLCLOSINGMARUBOZU.Plo
	-rm -f ./$(DEPDIR)/ta_CDLCONCEALBABYSWALL.Plo
	-rm -f ./$(DEPDIR)/ta_CDLCOUNTERATTACK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDARKCLOUDCOVER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDOJISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDRAGONFLYDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLENGULFING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLEVENINGDOJISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLEVENINGSTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLGAPSIDESIDEWHITE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLGRAVESTONEDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHAMMER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHANGINGMAN.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHARAMI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHARAMICROSS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHIGHWAVE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHIKKAKE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHIKKAKEMOD.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHOMINGPIGEON.Plo
	-rm -f ./$(DEPDIR)/ta_CDLIDENTICAL3CROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLINNECK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLINVERTEDHAMMER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLKICKING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLKICKINGBYLENGTH.Plo
	-rm -f ./$(DEPDIR)/ta_CDLLADDERBOTTOM.Plo
	-rm -f ./$(DEPDIR)/ta_CDLLONGLEGGEDDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLLONGLINE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMARUBOZU.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMATCHINGLOW.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMATHOLD.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMORNINGDOJISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMORNINGSTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLONNECK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLPIERCING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLRICKSHAWMAN.Plo
	-rm -f ./$(DEPDIR)/ta_CDLRISEFALL3METHODS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSEPARATINGLINES.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSHOOTINGSTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSHORTLINE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSPINNINGTOP.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSTALLEDPATTERN.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSTICKSANDWICH.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTAKURI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTASUKIGAP.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTHRUSTING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTRISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLUNIQUE3RIVER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLUPSIDEGAP2CROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLXSIDEGAP3METHODS.Plo
	-rm -f ./$(DEPDIR)/ta_CEIL.Plo
	-rm -f ./$(DEPDIR)/ta_CMO.Plo
	-rm -f ./$(DEPDIR)/ta_CORREL.Plo
	-rm -f ./$(DEPDIR)/ta_COS.Plo
	-rm -f ./$(DEPDIR)/ta_COSH.Plo
	-rm -f ./$(DEPDIR)/ta_DEMA.Plo
	-rm -f ./$(DEPDIR)/ta_DIV.Plo
	-rm -f ./$(DEPDIR)/ta_DX.Plo
	-rm -f ./$(DEPDIR)/ta_EMA.Plo
	-rm -f ./$(DEPDIR)/ta_EXP.Plo
	-rm -f ./$(DEPDIR)/ta_FLOOR.Plo
	-rm -f ./$(DEPDIR)/ta_HT_DCPERIOD.Plo
	-rm -f ./$(DEPDIR)/ta_HT_DCPHASE.Plo
	-rm -f ./$(DEPDIR)/ta_HT_PHASOR.Plo
	-rm -f ./$(DEPDIR)/ta_HT_SINE.Plo
	-rm -f ./$(DEPDIR)/ta_HT_TRENDLINE.Plo
	-rm -f ./$(DEPDIR)/ta_HT_TRENDMODE.Plo
	-rm -f ./$(DEPDIR)/ta_IMI.Plo
	-rm -f ./$(DEPDIR)/ta_KAMA.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG_ANGLE.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG_INTERCEPT.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG_SLOPE.Plo
	-rm -f ./$(DEPDIR)/ta_LN.Plo
	-rm -f ./$(DEPDIR)/ta_LOG10.Plo
	-rm -f ./$(DEPDIR)/ta_MA.Plo
	-rm -f ./$(DEPDIR)/ta_MACD.Plo
	-rm -f ./$(DEPDIR)/ta_MACDEXT.Plo
	-rm -f ./$(DEPDIR)/ta_MACDFIX.Plo
	-rm -f ./$(DEPDIR)/ta_MAMA.Plo
	-rm -f ./$(DEPDIR)/ta_MAVP.Plo
	-rm -f ./$(DEPDIR)/ta_MAX.Plo
	-rm -f ./$(DEPDIR)/ta_MAXINDEX.Plo
	-rm -f ./$(DEPDIR)/ta_MEDPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_MFI.Plo
	-rm -f ./$(DEPDIR)/ta_MIDPOINT.Plo
	-rm -f ./$(DEPDIR)/ta_MIDPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_MIN.Plo
	-rm -f ./$(DEPDIR)/ta_MININDEX.Plo
	-rm -f ./$(DEPDIR)/ta_MINMAX.Plo
	-rm -f ./$(DEPDIR)/ta_MINMAXINDEX.Plo
	-rm -f ./$(DEPDIR)/ta_MINUS_DI.Plo
	-rm -f ./$(DEPDIR)/ta_MINUS_DM.Plo
	-rm -f ./$(DEPDIR)/ta_MOM.Plo
	-rm -f ./$(DEPDIR)/ta_MULT.Plo
	-rm -f ./$(DEPDIR)/ta_NATR.Plo
	-rm -f ./$(DEPDIR)/ta_OBV.Plo
	-rm -f ./$(DEPDIR)/ta_PLUS_DI.Plo
	-rm -f ./$(DEPDIR)/ta_PLUS_DM.Plo
	-rm -f ./$(DEPDIR)/ta_PPO.Plo
	-rm -f ./$(DEPDIR)/ta_ROC.Plo
	-rm -f ./$(DEPDIR)/ta_ROCP.Plo
	-rm -f ./$(DEPDIR)/ta_ROCR.Plo
	-rm -f ./$(DEPDIR)/ta_ROCR100.Plo
	-rm -f ./$(DEPDIR)/ta_RSI.Plo
	-rm -f ./$(DEPDIR)/ta_SAR.Plo
	-rm -f ./$(DEPDIR)/ta_SAREXT.Plo
	-rm -f ./$(DEPDIR)/ta_SIN.Plo
	-rm -f ./$(DEPDIR)/ta_SINH.Plo
	-rm -f ./$(DEPDIR)/ta_SMA.Plo
	-rm -f ./$(DEPDIR)/ta_SQRT.Plo
	-rm -f ./$(DEPDIR)/ta_STDDEV.Plo
	-rm -f ./$(DEPDIR)/ta_STOCH.Plo
	-rm -f ./$(DEPDIR)/ta_STOCHF.Plo
	-rm -f ./$(DEPDIR)/ta_STOCHRSI.Plo
	-rm -f ./$(DEPDIR)/ta_SUB.Plo
	-rm -f ./$(DEPDIR)/ta_SUM.Plo
	-rm -f ./$(DEPDIR)/ta_T3.Plo
	-rm -f ./$(DEPDIR)/ta_TAN.Plo
	-rm -f ./$(DEPDIR)/ta_TANH.Plo
	-rm -f ./$(DEPDIR)/ta_TEMA.Plo
	-rm -f ./$(DEPDIR)/ta_TRANGE.Plo
	-rm -f ./$(DEPDIR)/ta_TRIMA.Plo
	-rm -f ./$(DEPDIR)/ta_TRIX.Plo
	-rm -f ./$(DEPDIR)/ta_TSF.Plo
	-rm -f ./$(DEPDIR)/ta_TYPPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_ULTOSC.Plo
	-rm -f ./$(DEPDIR)/ta_VAR.Plo
	-rm -f ./$(DEPDIR)/ta_WCLPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_WILLR.Plo
	-rm -f ./$(DEPDIR)/ta_WMA.Plo
	-rm -f ./$(DEPDIR)/ta_utility.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-libta_funcHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/ta_ACCBANDS.Plo
	-rm -f ./$(DEPDIR)/ta_ACOS.Plo
	-rm -f ./$(DEPDIR)/ta_AD.Plo
	-rm -f ./$(DEPDIR)/ta_ADD.Plo
	-rm -f ./$(DEPDIR)/ta_ADOSC.Plo
	-rm -f ./$(DEPDIR)/ta_ADX.Plo
	-rm -f ./$(DEPDIR)/ta_ADXR.Plo
	-rm -f ./$(DEPDIR)/ta_APO.Plo
	-rm -f ./$(DEPDIR)/ta_AROON.Plo
	-rm -f ./$(DEPDIR)/ta_AROONOSC.Plo
	-rm -f ./$(DEPDIR)/ta_ASIN.Plo
	-rm -f ./$(DEPDIR)/ta_ATAN.Plo
	-rm -f ./$(DEPDIR)/ta_ATR.Plo
	-rm -f ./$(DEPDIR)/ta_AVGDEV.Plo
	-rm -f ./$(DEPDIR)/ta_AVGPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_BBANDS.Plo
	-rm -f ./$(DEPDIR)/ta_BETA.Plo
	-rm -f ./$(DEPDIR)/ta_BOP.Plo
	-rm -f ./$(DEPDIR)/ta_CCI.Plo
	-rm -f ./$(DEPDIR)/ta_CDL2CROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3BLACKCROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3INSIDE.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3LINESTRIKE.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3OUTSIDE.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3STARSINSOUTH.Plo
	-rm -f ./$(DEPDIR)/ta_CDL3WHITESOLDIERS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLABANDONEDBABY.Plo
	-rm -f ./$(DEPDIR)/ta_CDLADVANCEBLOCK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLBELTHOLD.Plo
	-rm -f ./$(DEPDIR)/ta_CDLBREAKAWAY.Plo
	-rm -f ./$(DEPDIR)/ta_CDLCLOSINGMARUBOZU.Plo
	-rm -f ./$(DEPDIR)/ta_CDLCONCEALBABYSWALL.Plo
	-rm -f ./$(DEPDIR)/ta_CDLCOUNTERATTACK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDARKCLOUDCOVER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDOJISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLDRAGONFLYDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLENGULFING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLEVENINGDOJISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLEVENINGSTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLGAPSIDESIDEWHITE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLGRAVESTONEDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHAMMER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHANGINGMAN.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHARAMI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHARAMICROSS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHIGHWAVE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHIKKAKE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHIKKAKEMOD.Plo
	-rm -f ./$(DEPDIR)/ta_CDLHOMINGPIGEON.Plo
	-rm -f ./$(DEPDIR)/ta_CDLIDENTICAL3CROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLINNECK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLINVERTEDHAMMER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLKICKING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLKICKINGBYLENGTH.Plo
	-rm -f ./$(DEPDIR)/ta_CDLLADDERBOTTOM.Plo
	-rm -f ./$(DEPDIR)/ta_CDLLONGLEGGEDDOJI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLLONGLINE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMARUBOZU.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMATCHINGLOW.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMATHOLD.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMORNINGDOJISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLMORNINGSTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLONNECK.Plo
	-rm -f ./$(DEPDIR)/ta_CDLPIERCING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLRICKSHAWMAN.Plo
	-rm -f ./$(DEPDIR)/ta_CDLRISEFALL3METHODS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSEPARATINGLINES.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSHOOTINGSTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSHORTLINE.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSPINNINGTOP.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSTALLEDPATTERN.Plo
	-rm -f ./$(DEPDIR)/ta_CDLSTICKSANDWICH.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTAKURI.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTASUKIGAP.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTHRUSTING.Plo
	-rm -f ./$(DEPDIR)/ta_CDLTRISTAR.Plo
	-rm -f ./$(DEPDIR)/ta_CDLUNIQUE3RIVER.Plo
	-rm -f ./$(DEPDIR)/ta_CDLUPSIDEGAP2CROWS.Plo
	-rm -f ./$(DEPDIR)/ta_CDLXSIDEGAP3METHODS.Plo
	-rm -f ./$(DEPDIR)/ta_CEIL.Plo
	-rm -f ./$(DEPDIR)/ta_CMO.Plo
	-rm -f ./$(DEPDIR)/ta_CORREL.Plo
	-rm -f ./$(DEPDIR)/ta_COS.Plo
	-rm -f ./$(DEPDIR)/ta_COSH.Plo
	-rm -f ./$(DEPDIR)/ta_DEMA.Plo
	-rm -f ./$(DEPDIR)/ta_DIV.Plo
	-rm -f ./$(DEPDIR)/ta_DX.Plo
	-rm -f ./$(DEPDIR)/ta_EMA.Plo
	-rm -f ./$(DEPDIR)/ta_EXP.Plo
	-rm -f ./$(DEPDIR)/ta_FLOOR.Plo
	-rm -f ./$(DEPDIR)/ta_HT_DCPERIOD.Plo
	-rm -f ./$(DEPDIR)/ta_HT_DCPHASE.Plo
	-rm -f ./$(DEPDIR)/ta_HT_PHASOR.Plo
	-rm -f ./$(DEPDIR)/ta_HT_SINE.Plo
	-rm -f ./$(DEPDIR)/ta_HT_TRENDLINE.Plo
	-rm -f ./$(DEPDIR)/ta_HT_TRENDMODE.Plo
	-rm -f ./$(DEPDIR)/ta_IMI.Plo
	-rm -f ./$(DEPDIR)/ta_KAMA.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG_ANGLE.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG_INTERCEPT.Plo
	-rm -f ./$(DEPDIR)/ta_LINEARREG_SLOPE.Plo
	-rm -f ./$(DEPDIR)/ta_LN.Plo
	-rm -f ./$(DEPDIR)/ta_LOG10.Plo
	-rm -f ./$(DEPDIR)/ta_MA.Plo
	-rm -f ./$(DEPDIR)/ta_MACD.Plo
	-rm -f ./$(DEPDIR)/ta_MACDEXT.Plo
	-rm -f ./$(DEPDIR)/ta_MACDFIX.Plo
	-rm -f ./$(DEPDIR)/ta_MAMA.Plo
	-rm -f ./$(DEPDIR)/ta_MAVP.Plo
	-rm -f ./$(DEPDIR)/ta_MAX.Plo
	-rm -f ./$(DEPDIR)/ta_MAXINDEX.Plo
	-rm -f ./$(DEPDIR)/ta_MEDPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_MFI.Plo
	-rm -f ./$(DEPDIR)/ta_MIDPOINT.Plo
	-rm -f ./$(DEPDIR)/ta_MIDPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_MIN.Plo
	-rm -f ./$(DEPDIR)/ta_MININDEX.Plo
	-rm -f ./$(DEPDIR)/ta_MINMAX.Plo
	-rm -f ./$(DEPDIR)/ta_MINMAXINDEX.Plo
	-rm -f ./$(DEPDIR)/ta_MINUS_DI.Plo
	-rm -f ./$(DEPDIR)/ta_MINUS_DM.Plo
	-rm -f ./$(DEPDIR)/ta_MOM.Plo
	-rm -f ./$(DEPDIR)/ta_MULT.Plo
	-rm -f ./$(DEPDIR)/ta_NATR.Plo
	-rm -f ./$(DEPDIR)/ta_OBV.Plo
	-rm -f ./$(DEPDIR)/ta_PLUS_DI.Plo
	-rm -f ./$(DEPDIR)/ta_PLUS_DM.Plo
	-rm -f ./$(DEPDIR)/ta_PPO.Plo
	-rm -f ./$(DEPDIR)/ta_ROC.Plo
	-rm -f ./$(DEPDIR)/ta_ROCP.Plo
	-rm -f ./$(DEPDIR)/ta_ROCR.Plo
	-rm -f ./$(DEPDIR)/ta_ROCR100.Plo
	-rm -f ./$(DEPDIR)/ta_RSI.Plo
	-rm -f ./$(DEPDIR)/ta_SAR.Plo
	-rm -f ./$(DEPDIR)/ta_SAREXT.Plo
	-rm -f ./$(DEPDIR)/ta_SIN.Plo
	-rm -f ./$(DEPDIR)/ta_SINH.Plo
	-rm -f ./$(DEPDIR)/ta_SMA.Plo
	-rm -f ./$(DEPDIR)/ta_SQRT.Plo
	-rm -f ./$(DEPDIR)/ta_STDDEV.Plo
	-rm -f ./$(DEPDIR)/ta_STOCH.Plo
	-rm -f ./$(DEPDIR)/ta_STOCHF.Plo
	-rm -f ./$(DEPDIR)/ta_STOCHRSI.Plo
	-rm -f ./$(DEPDIR)/ta_SUB.Plo
	-rm -f ./$(DEPDIR)/ta_SUM.Plo
	-rm -f ./$(DEPDIR)/ta_T3.Plo
	-rm -f ./$(DEPDIR)/ta_TAN.Plo
	-rm -f ./$(DEPDIR)/ta_TANH.Plo
	-rm -f ./$(DEPDIR)/ta_TEMA.Plo
	-rm -f ./$(DEPDIR)/ta_TRANGE.Plo
	-rm -f ./$(DEPDIR)/ta_TRIMA.Plo
	-rm -f ./$(DEPDIR)/ta_TRIX.Plo
	-rm -f ./$(DEPDIR)/ta_TSF.Plo
	-rm -f ./$(DEPDIR)/ta_TYPPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_ULTOSC.Plo
	-rm -f ./$(DEPDIR)/ta_VAR.Plo
	-rm -f ./$(DEPDIR)/ta_WCLPRICE.Plo
	-rm -f ./$(DEPDIR)/ta_WILLR.Plo
	-rm -f ./$(DEPDIR)/ta_WMA.Plo
	-rm -f ./$(DEPDIR)/ta_utility.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libta_funcHEADERS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libtool clean-noinstLTLIBRARIES \
	cscopelist-am ctags ctags-am distclean distclean-compile \
	distclean-generic distclean-libtool distclean-tags distdir dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-libta_funcHEADERS \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-libta_funcHEADERS

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
