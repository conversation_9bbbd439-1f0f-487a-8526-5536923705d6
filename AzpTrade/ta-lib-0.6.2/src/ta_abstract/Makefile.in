# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@


VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = src/ta_abstract
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(libta_abstract_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
LTLIBRARIES = $(noinst_LTLIBRARIES)
libta_abstract_la_LIBADD =
am__dirstamp = $(am__leading_dot)dirstamp
am_libta_abstract_la_OBJECTS = libta_abstract_la-ta_group_idx.lo \
	libta_abstract_la-ta_def_ui.lo \
	libta_abstract_la-ta_abstract.lo \
	libta_abstract_la-ta_func_api.lo \
	frames/libta_abstract_la-ta_frame.lo \
	tables/libta_abstract_la-table_a.lo \
	tables/libta_abstract_la-table_b.lo \
	tables/libta_abstract_la-table_c.lo \
	tables/libta_abstract_la-table_d.lo \
	tables/libta_abstract_la-table_e.lo \
	tables/libta_abstract_la-table_f.lo \
	tables/libta_abstract_la-table_g.lo \
	tables/libta_abstract_la-table_h.lo \
	tables/libta_abstract_la-table_i.lo \
	tables/libta_abstract_la-table_j.lo \
	tables/libta_abstract_la-table_k.lo \
	tables/libta_abstract_la-table_l.lo \
	tables/libta_abstract_la-table_m.lo \
	tables/libta_abstract_la-table_n.lo \
	tables/libta_abstract_la-table_o.lo \
	tables/libta_abstract_la-table_p.lo \
	tables/libta_abstract_la-table_q.lo \
	tables/libta_abstract_la-table_r.lo \
	tables/libta_abstract_la-table_s.lo \
	tables/libta_abstract_la-table_t.lo \
	tables/libta_abstract_la-table_u.lo \
	tables/libta_abstract_la-table_v.lo \
	tables/libta_abstract_la-table_w.lo \
	tables/libta_abstract_la-table_x.lo \
	tables/libta_abstract_la-table_y.lo \
	tables/libta_abstract_la-table_z.lo
libta_abstract_la_OBJECTS = $(am_libta_abstract_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libta_abstract_gc_la_LIBADD =
am__objects_1 = libta_abstract_gc_la-ta_group_idx.lo \
	libta_abstract_gc_la-ta_def_ui.lo \
	libta_abstract_gc_la-ta_abstract.lo \
	libta_abstract_gc_la-ta_func_api.lo \
	frames/libta_abstract_gc_la-ta_frame.lo \
	tables/libta_abstract_gc_la-table_a.lo \
	tables/libta_abstract_gc_la-table_b.lo \
	tables/libta_abstract_gc_la-table_c.lo \
	tables/libta_abstract_gc_la-table_d.lo \
	tables/libta_abstract_gc_la-table_e.lo \
	tables/libta_abstract_gc_la-table_f.lo \
	tables/libta_abstract_gc_la-table_g.lo \
	tables/libta_abstract_gc_la-table_h.lo \
	tables/libta_abstract_gc_la-table_i.lo \
	tables/libta_abstract_gc_la-table_j.lo \
	tables/libta_abstract_gc_la-table_k.lo \
	tables/libta_abstract_gc_la-table_l.lo \
	tables/libta_abstract_gc_la-table_m.lo \
	tables/libta_abstract_gc_la-table_n.lo \
	tables/libta_abstract_gc_la-table_o.lo \
	tables/libta_abstract_gc_la-table_p.lo \
	tables/libta_abstract_gc_la-table_q.lo \
	tables/libta_abstract_gc_la-table_r.lo \
	tables/libta_abstract_gc_la-table_s.lo \
	tables/libta_abstract_gc_la-table_t.lo \
	tables/libta_abstract_gc_la-table_u.lo \
	tables/libta_abstract_gc_la-table_v.lo \
	tables/libta_abstract_gc_la-table_w.lo \
	tables/libta_abstract_gc_la-table_x.lo \
	tables/libta_abstract_gc_la-table_y.lo \
	tables/libta_abstract_gc_la-table_z.lo
am_libta_abstract_gc_la_OBJECTS = $(am__objects_1)
libta_abstract_gc_la_OBJECTS = $(am_libta_abstract_gc_la_OBJECTS)
libta_abstract_gc_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(AM_CFLAGS) $(CFLAGS) $(libta_abstract_gc_la_LDFLAGS) \
	$(LDFLAGS) -o $@
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)/include
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade =  \
	./$(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo \
	./$(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo \
	./$(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo \
	./$(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo \
	./$(DEPDIR)/libta_abstract_la-ta_abstract.Plo \
	./$(DEPDIR)/libta_abstract_la-ta_def_ui.Plo \
	./$(DEPDIR)/libta_abstract_la-ta_func_api.Plo \
	./$(DEPDIR)/libta_abstract_la-ta_group_idx.Plo \
	frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo \
	frames/$(DEPDIR)/libta_abstract_la-ta_frame.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Plo \
	tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_a.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_b.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_c.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_d.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_e.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_f.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_g.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_h.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_i.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_j.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_k.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_l.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_m.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_n.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_o.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_p.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_q.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_r.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_s.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_t.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_u.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_v.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_w.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_x.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_y.Plo \
	tables/$(DEPDIR)/libta_abstract_la-table_z.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libta_abstract_la_SOURCES) $(libta_abstract_gc_la_SOURCES)
DIST_SOURCES = $(libta_abstract_la_SOURCES) \
	$(libta_abstract_gc_la_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(libta_abstractdir)"
HEADERS = $(libta_abstract_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBM = @LIBM@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
POW_LIB = @POW_LIB@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TALIB_LIBRARY_VERSION = @TALIB_LIBRARY_VERSION@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
noinst_LTLIBRARIES = libta_abstract.la libta_abstract_gc.la
libta_abstract_la_SOURCES = ta_group_idx.c \
	ta_def_ui.c \
	ta_abstract.c \
	ta_func_api.c \
	frames/ta_frame.c \
	tables/table_a.c \
	tables/table_b.c \
	tables/table_c.c \
	tables/table_d.c \
	tables/table_e.c \
	tables/table_f.c \
	tables/table_g.c \
	tables/table_h.c \
	tables/table_i.c \
	tables/table_j.c \
	tables/table_k.c \
	tables/table_l.c \
	tables/table_m.c \
	tables/table_n.c \
	tables/table_o.c \
	tables/table_p.c \
	tables/table_q.c \
	tables/table_r.c \
	tables/table_s.c \
	tables/table_t.c \
	tables/table_u.c \
	tables/table_v.c \
	tables/table_w.c \
	tables/table_x.c \
	tables/table_y.c \
	tables/table_z.c

libta_abstract_gc_la_SOURCES = $(libta_abstract_la_SOURCES)
libta_abstract_gc_la_LDFLAGS = $(libta_abstract_la_LDFLAGS)
libta_abstract_la_CPPFLAGS = -I../ta_common/ -Iframes/

# The 'gc' version is a minimal version used to just to compile gen_code
libta_abstract_gc_la_CPPFLAGS = -DTA_GEN_CODE $(libta_abstract_la_CPPFLAGS)
libta_abstractdir = $(includedir)/ta-lib/
libta_abstract_HEADERS = ../../include/ta_defs.h \
	../../include/ta_libc.h \
	../../include/ta_abstract.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/ta_abstract/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/ta_abstract/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
frames/$(am__dirstamp):
	@$(MKDIR_P) frames
	@: > frames/$(am__dirstamp)
frames/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) frames/$(DEPDIR)
	@: > frames/$(DEPDIR)/$(am__dirstamp)
frames/libta_abstract_la-ta_frame.lo: frames/$(am__dirstamp) \
	frames/$(DEPDIR)/$(am__dirstamp)
tables/$(am__dirstamp):
	@$(MKDIR_P) tables
	@: > tables/$(am__dirstamp)
tables/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) tables/$(DEPDIR)
	@: > tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_a.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_b.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_c.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_d.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_e.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_f.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_g.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_h.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_i.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_j.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_k.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_l.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_m.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_n.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_o.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_p.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_q.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_r.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_s.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_t.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_u.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_v.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_w.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_x.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_y.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_la-table_z.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)

libta_abstract.la: $(libta_abstract_la_OBJECTS) $(libta_abstract_la_DEPENDENCIES) $(EXTRA_libta_abstract_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libta_abstract_la_OBJECTS) $(libta_abstract_la_LIBADD) $(LIBS)
frames/libta_abstract_gc_la-ta_frame.lo: frames/$(am__dirstamp) \
	frames/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_a.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_b.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_c.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_d.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_e.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_f.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_g.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_h.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_i.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_j.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_k.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_l.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_m.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_n.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_o.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_p.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_q.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_r.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_s.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_t.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_u.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_v.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_w.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_x.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_y.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)
tables/libta_abstract_gc_la-table_z.lo: tables/$(am__dirstamp) \
	tables/$(DEPDIR)/$(am__dirstamp)

libta_abstract_gc.la: $(libta_abstract_gc_la_OBJECTS) $(libta_abstract_gc_la_DEPENDENCIES) $(EXTRA_libta_abstract_gc_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libta_abstract_gc_la_LINK)  $(libta_abstract_gc_la_OBJECTS) $(libta_abstract_gc_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f frames/*.$(OBJEXT)
	-rm -f frames/*.lo
	-rm -f tables/*.$(OBJEXT)
	-rm -f tables/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_la-ta_abstract.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_la-ta_def_ui.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_la-ta_func_api.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/libta_abstract_la-ta_group_idx.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@frames/$(DEPDIR)/libta_abstract_la-ta_frame.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_a.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_b.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_c.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_d.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_e.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_f.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_g.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_h.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_i.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_j.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_k.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_l.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_m.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_n.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_o.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_p.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_q.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_r.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_s.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_t.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_u.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_v.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_w.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_x.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_y.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tables/$(DEPDIR)/libta_abstract_la-table_z.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

libta_abstract_la-ta_group_idx.lo: ta_group_idx.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_group_idx.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_group_idx.Tpo -c -o libta_abstract_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_la-ta_group_idx.Tpo $(DEPDIR)/libta_abstract_la-ta_group_idx.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_group_idx.c' object='libta_abstract_la-ta_group_idx.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c

libta_abstract_la-ta_def_ui.lo: ta_def_ui.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_def_ui.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_def_ui.Tpo -c -o libta_abstract_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_la-ta_def_ui.Tpo $(DEPDIR)/libta_abstract_la-ta_def_ui.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_def_ui.c' object='libta_abstract_la-ta_def_ui.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c

libta_abstract_la-ta_abstract.lo: ta_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_abstract.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_abstract.Tpo -c -o libta_abstract_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_la-ta_abstract.Tpo $(DEPDIR)/libta_abstract_la-ta_abstract.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_abstract.c' object='libta_abstract_la-ta_abstract.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c

libta_abstract_la-ta_func_api.lo: ta_func_api.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_func_api.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_func_api.Tpo -c -o libta_abstract_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_la-ta_func_api.Tpo $(DEPDIR)/libta_abstract_la-ta_func_api.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_func_api.c' object='libta_abstract_la-ta_func_api.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c

frames/libta_abstract_la-ta_frame.lo: frames/ta_frame.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT frames/libta_abstract_la-ta_frame.lo -MD -MP -MF frames/$(DEPDIR)/libta_abstract_la-ta_frame.Tpo -c -o frames/libta_abstract_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) frames/$(DEPDIR)/libta_abstract_la-ta_frame.Tpo frames/$(DEPDIR)/libta_abstract_la-ta_frame.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='frames/ta_frame.c' object='frames/libta_abstract_la-ta_frame.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o frames/libta_abstract_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c

tables/libta_abstract_la-table_a.lo: tables/table_a.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_a.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_a.Tpo -c -o tables/libta_abstract_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_a.Tpo tables/$(DEPDIR)/libta_abstract_la-table_a.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_a.c' object='tables/libta_abstract_la-table_a.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c

tables/libta_abstract_la-table_b.lo: tables/table_b.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_b.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_b.Tpo -c -o tables/libta_abstract_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_b.Tpo tables/$(DEPDIR)/libta_abstract_la-table_b.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_b.c' object='tables/libta_abstract_la-table_b.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c

tables/libta_abstract_la-table_c.lo: tables/table_c.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_c.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_c.Tpo -c -o tables/libta_abstract_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_c.Tpo tables/$(DEPDIR)/libta_abstract_la-table_c.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_c.c' object='tables/libta_abstract_la-table_c.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c

tables/libta_abstract_la-table_d.lo: tables/table_d.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_d.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_d.Tpo -c -o tables/libta_abstract_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_d.Tpo tables/$(DEPDIR)/libta_abstract_la-table_d.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_d.c' object='tables/libta_abstract_la-table_d.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c

tables/libta_abstract_la-table_e.lo: tables/table_e.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_e.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_e.Tpo -c -o tables/libta_abstract_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_e.Tpo tables/$(DEPDIR)/libta_abstract_la-table_e.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_e.c' object='tables/libta_abstract_la-table_e.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c

tables/libta_abstract_la-table_f.lo: tables/table_f.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_f.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_f.Tpo -c -o tables/libta_abstract_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_f.Tpo tables/$(DEPDIR)/libta_abstract_la-table_f.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_f.c' object='tables/libta_abstract_la-table_f.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c

tables/libta_abstract_la-table_g.lo: tables/table_g.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_g.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_g.Tpo -c -o tables/libta_abstract_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_g.Tpo tables/$(DEPDIR)/libta_abstract_la-table_g.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_g.c' object='tables/libta_abstract_la-table_g.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c

tables/libta_abstract_la-table_h.lo: tables/table_h.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_h.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_h.Tpo -c -o tables/libta_abstract_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_h.Tpo tables/$(DEPDIR)/libta_abstract_la-table_h.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_h.c' object='tables/libta_abstract_la-table_h.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c

tables/libta_abstract_la-table_i.lo: tables/table_i.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_i.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_i.Tpo -c -o tables/libta_abstract_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_i.Tpo tables/$(DEPDIR)/libta_abstract_la-table_i.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_i.c' object='tables/libta_abstract_la-table_i.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c

tables/libta_abstract_la-table_j.lo: tables/table_j.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_j.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_j.Tpo -c -o tables/libta_abstract_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_j.Tpo tables/$(DEPDIR)/libta_abstract_la-table_j.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_j.c' object='tables/libta_abstract_la-table_j.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c

tables/libta_abstract_la-table_k.lo: tables/table_k.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_k.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_k.Tpo -c -o tables/libta_abstract_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_k.Tpo tables/$(DEPDIR)/libta_abstract_la-table_k.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_k.c' object='tables/libta_abstract_la-table_k.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c

tables/libta_abstract_la-table_l.lo: tables/table_l.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_l.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_l.Tpo -c -o tables/libta_abstract_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_l.Tpo tables/$(DEPDIR)/libta_abstract_la-table_l.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_l.c' object='tables/libta_abstract_la-table_l.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c

tables/libta_abstract_la-table_m.lo: tables/table_m.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_m.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_m.Tpo -c -o tables/libta_abstract_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_m.Tpo tables/$(DEPDIR)/libta_abstract_la-table_m.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_m.c' object='tables/libta_abstract_la-table_m.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c

tables/libta_abstract_la-table_n.lo: tables/table_n.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_n.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_n.Tpo -c -o tables/libta_abstract_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_n.Tpo tables/$(DEPDIR)/libta_abstract_la-table_n.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_n.c' object='tables/libta_abstract_la-table_n.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c

tables/libta_abstract_la-table_o.lo: tables/table_o.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_o.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_o.Tpo -c -o tables/libta_abstract_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_o.Tpo tables/$(DEPDIR)/libta_abstract_la-table_o.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_o.c' object='tables/libta_abstract_la-table_o.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c

tables/libta_abstract_la-table_p.lo: tables/table_p.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_p.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_p.Tpo -c -o tables/libta_abstract_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_p.Tpo tables/$(DEPDIR)/libta_abstract_la-table_p.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_p.c' object='tables/libta_abstract_la-table_p.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c

tables/libta_abstract_la-table_q.lo: tables/table_q.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_q.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_q.Tpo -c -o tables/libta_abstract_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_q.Tpo tables/$(DEPDIR)/libta_abstract_la-table_q.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_q.c' object='tables/libta_abstract_la-table_q.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c

tables/libta_abstract_la-table_r.lo: tables/table_r.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_r.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_r.Tpo -c -o tables/libta_abstract_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_r.Tpo tables/$(DEPDIR)/libta_abstract_la-table_r.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_r.c' object='tables/libta_abstract_la-table_r.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c

tables/libta_abstract_la-table_s.lo: tables/table_s.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_s.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_s.Tpo -c -o tables/libta_abstract_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_s.Tpo tables/$(DEPDIR)/libta_abstract_la-table_s.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_s.c' object='tables/libta_abstract_la-table_s.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c

tables/libta_abstract_la-table_t.lo: tables/table_t.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_t.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_t.Tpo -c -o tables/libta_abstract_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_t.Tpo tables/$(DEPDIR)/libta_abstract_la-table_t.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_t.c' object='tables/libta_abstract_la-table_t.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c

tables/libta_abstract_la-table_u.lo: tables/table_u.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_u.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_u.Tpo -c -o tables/libta_abstract_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_u.Tpo tables/$(DEPDIR)/libta_abstract_la-table_u.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_u.c' object='tables/libta_abstract_la-table_u.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c

tables/libta_abstract_la-table_v.lo: tables/table_v.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_v.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_v.Tpo -c -o tables/libta_abstract_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_v.Tpo tables/$(DEPDIR)/libta_abstract_la-table_v.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_v.c' object='tables/libta_abstract_la-table_v.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c

tables/libta_abstract_la-table_w.lo: tables/table_w.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_w.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_w.Tpo -c -o tables/libta_abstract_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_w.Tpo tables/$(DEPDIR)/libta_abstract_la-table_w.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_w.c' object='tables/libta_abstract_la-table_w.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c

tables/libta_abstract_la-table_x.lo: tables/table_x.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_x.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_x.Tpo -c -o tables/libta_abstract_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_x.Tpo tables/$(DEPDIR)/libta_abstract_la-table_x.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_x.c' object='tables/libta_abstract_la-table_x.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c

tables/libta_abstract_la-table_y.lo: tables/table_y.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_y.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_y.Tpo -c -o tables/libta_abstract_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_y.Tpo tables/$(DEPDIR)/libta_abstract_la-table_y.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_y.c' object='tables/libta_abstract_la-table_y.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c

tables/libta_abstract_la-table_z.lo: tables/table_z.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_la-table_z.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_la-table_z.Tpo -c -o tables/libta_abstract_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_la-table_z.Tpo tables/$(DEPDIR)/libta_abstract_la-table_z.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_z.c' object='tables/libta_abstract_la-table_z.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c

libta_abstract_gc_la-ta_group_idx.lo: ta_group_idx.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_group_idx.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Tpo -c -o libta_abstract_gc_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_group_idx.c' object='libta_abstract_gc_la-ta_group_idx.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c

libta_abstract_gc_la-ta_def_ui.lo: ta_def_ui.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_def_ui.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Tpo -c -o libta_abstract_gc_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_def_ui.c' object='libta_abstract_gc_la-ta_def_ui.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c

libta_abstract_gc_la-ta_abstract.lo: ta_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_abstract.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_abstract.Tpo -c -o libta_abstract_gc_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_gc_la-ta_abstract.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_abstract.c' object='libta_abstract_gc_la-ta_abstract.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c

libta_abstract_gc_la-ta_func_api.lo: ta_func_api.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_func_api.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_func_api.Tpo -c -o libta_abstract_gc_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/libta_abstract_gc_la-ta_func_api.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_func_api.c' object='libta_abstract_gc_la-ta_func_api.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c

frames/libta_abstract_gc_la-ta_frame.lo: frames/ta_frame.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT frames/libta_abstract_gc_la-ta_frame.lo -MD -MP -MF frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Tpo -c -o frames/libta_abstract_gc_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Tpo frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='frames/ta_frame.c' object='frames/libta_abstract_gc_la-ta_frame.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o frames/libta_abstract_gc_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c

tables/libta_abstract_gc_la-table_a.lo: tables/table_a.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_a.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Tpo -c -o tables/libta_abstract_gc_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_a.c' object='tables/libta_abstract_gc_la-table_a.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c

tables/libta_abstract_gc_la-table_b.lo: tables/table_b.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_b.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Tpo -c -o tables/libta_abstract_gc_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_b.c' object='tables/libta_abstract_gc_la-table_b.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c

tables/libta_abstract_gc_la-table_c.lo: tables/table_c.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_c.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Tpo -c -o tables/libta_abstract_gc_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_c.c' object='tables/libta_abstract_gc_la-table_c.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c

tables/libta_abstract_gc_la-table_d.lo: tables/table_d.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_d.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Tpo -c -o tables/libta_abstract_gc_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_d.c' object='tables/libta_abstract_gc_la-table_d.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c

tables/libta_abstract_gc_la-table_e.lo: tables/table_e.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_e.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Tpo -c -o tables/libta_abstract_gc_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_e.c' object='tables/libta_abstract_gc_la-table_e.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c

tables/libta_abstract_gc_la-table_f.lo: tables/table_f.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_f.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Tpo -c -o tables/libta_abstract_gc_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_f.c' object='tables/libta_abstract_gc_la-table_f.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c

tables/libta_abstract_gc_la-table_g.lo: tables/table_g.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_g.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Tpo -c -o tables/libta_abstract_gc_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_g.c' object='tables/libta_abstract_gc_la-table_g.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c

tables/libta_abstract_gc_la-table_h.lo: tables/table_h.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_h.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Tpo -c -o tables/libta_abstract_gc_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_h.c' object='tables/libta_abstract_gc_la-table_h.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c

tables/libta_abstract_gc_la-table_i.lo: tables/table_i.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_i.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Tpo -c -o tables/libta_abstract_gc_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_i.c' object='tables/libta_abstract_gc_la-table_i.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c

tables/libta_abstract_gc_la-table_j.lo: tables/table_j.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_j.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Tpo -c -o tables/libta_abstract_gc_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_j.c' object='tables/libta_abstract_gc_la-table_j.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c

tables/libta_abstract_gc_la-table_k.lo: tables/table_k.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_k.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Tpo -c -o tables/libta_abstract_gc_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_k.c' object='tables/libta_abstract_gc_la-table_k.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c

tables/libta_abstract_gc_la-table_l.lo: tables/table_l.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_l.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Tpo -c -o tables/libta_abstract_gc_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_l.c' object='tables/libta_abstract_gc_la-table_l.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c

tables/libta_abstract_gc_la-table_m.lo: tables/table_m.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_m.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Tpo -c -o tables/libta_abstract_gc_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_m.c' object='tables/libta_abstract_gc_la-table_m.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c

tables/libta_abstract_gc_la-table_n.lo: tables/table_n.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_n.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Tpo -c -o tables/libta_abstract_gc_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_n.c' object='tables/libta_abstract_gc_la-table_n.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c

tables/libta_abstract_gc_la-table_o.lo: tables/table_o.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_o.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Tpo -c -o tables/libta_abstract_gc_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_o.c' object='tables/libta_abstract_gc_la-table_o.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c

tables/libta_abstract_gc_la-table_p.lo: tables/table_p.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_p.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Tpo -c -o tables/libta_abstract_gc_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_p.c' object='tables/libta_abstract_gc_la-table_p.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c

tables/libta_abstract_gc_la-table_q.lo: tables/table_q.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_q.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Tpo -c -o tables/libta_abstract_gc_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_q.c' object='tables/libta_abstract_gc_la-table_q.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c

tables/libta_abstract_gc_la-table_r.lo: tables/table_r.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_r.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Tpo -c -o tables/libta_abstract_gc_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_r.c' object='tables/libta_abstract_gc_la-table_r.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c

tables/libta_abstract_gc_la-table_s.lo: tables/table_s.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_s.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Tpo -c -o tables/libta_abstract_gc_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_s.c' object='tables/libta_abstract_gc_la-table_s.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c

tables/libta_abstract_gc_la-table_t.lo: tables/table_t.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_t.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Tpo -c -o tables/libta_abstract_gc_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_t.c' object='tables/libta_abstract_gc_la-table_t.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c

tables/libta_abstract_gc_la-table_u.lo: tables/table_u.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_u.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Tpo -c -o tables/libta_abstract_gc_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_u.c' object='tables/libta_abstract_gc_la-table_u.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c

tables/libta_abstract_gc_la-table_v.lo: tables/table_v.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_v.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Tpo -c -o tables/libta_abstract_gc_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_v.c' object='tables/libta_abstract_gc_la-table_v.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c

tables/libta_abstract_gc_la-table_w.lo: tables/table_w.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_w.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Tpo -c -o tables/libta_abstract_gc_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_w.c' object='tables/libta_abstract_gc_la-table_w.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c

tables/libta_abstract_gc_la-table_x.lo: tables/table_x.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_x.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Tpo -c -o tables/libta_abstract_gc_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_x.c' object='tables/libta_abstract_gc_la-table_x.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c

tables/libta_abstract_gc_la-table_y.lo: tables/table_y.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_y.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Tpo -c -o tables/libta_abstract_gc_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_y.c' object='tables/libta_abstract_gc_la-table_y.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c

tables/libta_abstract_gc_la-table_z.lo: tables/table_z.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT tables/libta_abstract_gc_la-table_z.lo -MD -MP -MF tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Tpo -c -o tables/libta_abstract_gc_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Tpo tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='tables/table_z.c' object='tables/libta_abstract_gc_la-table_z.lo' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o tables/libta_abstract_gc_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf frames/.libs frames/_libs
	-rm -rf tables/.libs tables/_libs
install-libta_abstractHEADERS: $(libta_abstract_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(libta_abstract_HEADERS)'; test -n "$(libta_abstractdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libta_abstractdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libta_abstractdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(libta_abstractdir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(libta_abstractdir)" || exit $$?; \
	done

uninstall-libta_abstractHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(libta_abstract_HEADERS)'; test -n "$(libta_abstractdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(libta_abstractdir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libta_abstractdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f frames/$(DEPDIR)/$(am__dirstamp)
	-rm -f frames/$(am__dirstamp)
	-rm -f tables/$(DEPDIR)/$(am__dirstamp)
	-rm -f tables/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_abstract.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_def_ui.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_func_api.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_group_idx.Plo
	-rm -f frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo
	-rm -f frames/$(DEPDIR)/libta_abstract_la-ta_frame.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_a.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_b.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_c.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_d.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_e.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_f.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_g.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_h.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_i.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_j.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_k.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_l.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_m.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_n.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_o.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_p.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_q.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_r.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_s.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_t.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_u.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_v.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_w.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_x.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_y.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_z.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-libta_abstractHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_abstract.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_def_ui.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_func_api.Plo
	-rm -f ./$(DEPDIR)/libta_abstract_la-ta_group_idx.Plo
	-rm -f frames/$(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo
	-rm -f frames/$(DEPDIR)/libta_abstract_la-ta_frame.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_a.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_b.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_c.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_d.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_e.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_f.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_g.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_h.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_i.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_j.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_k.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_l.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_m.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_n.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_o.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_p.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_q.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_r.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_s.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_t.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_u.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_v.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_w.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_x.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_y.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_gc_la-table_z.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_a.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_b.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_c.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_d.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_e.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_f.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_g.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_h.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_i.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_j.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_k.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_l.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_m.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_n.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_o.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_p.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_q.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_r.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_s.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_t.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_u.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_v.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_w.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_x.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_y.Plo
	-rm -f tables/$(DEPDIR)/libta_abstract_la-table_z.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libta_abstractHEADERS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libtool clean-noinstLTLIBRARIES \
	cscopelist-am ctags ctags-am distclean distclean-compile \
	distclean-generic distclean-libtool distclean-tags distdir dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-libta_abstractHEADERS \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool pdf pdf-am ps ps-am tags tags-am uninstall \
	uninstall-am uninstall-libta_abstractHEADERS

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
