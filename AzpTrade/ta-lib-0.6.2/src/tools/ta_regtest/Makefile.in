# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
noinst_PROGRAMS = ta_regtest$(EXEEXT)
subdir = src/tools/ta_regtest
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/libtool.m4 \
	$(top_srcdir)/m4/ltoptions.m4 $(top_srcdir)/m4/ltsugar.m4 \
	$(top_srcdir)/m4/ltversion.m4 $(top_srcdir)/m4/lt~obsolete.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
PROGRAMS = $(noinst_PROGRAMS)
am__dirstamp = $(am__leading_dot)dirstamp
am_ta_regtest_OBJECTS = ta_regtest-ta_regtest.$(OBJEXT) \
	ta_regtest-test_data.$(OBJEXT) ta_regtest-test_util.$(OBJEXT) \
	ta_regtest-test_abstract.$(OBJEXT) \
	ta_test_func/ta_regtest-test_adx.$(OBJEXT) \
	ta_test_func/ta_regtest-test_mom.$(OBJEXT) \
	ta_test_func/ta_regtest-test_sar.$(OBJEXT) \
	ta_test_func/ta_regtest-test_rsi.$(OBJEXT) \
	ta_test_func/ta_regtest-test_candlestick.$(OBJEXT) \
	ta_test_func/ta_regtest-test_per_ema.$(OBJEXT) \
	ta_test_func/ta_regtest-test_per_hlc.$(OBJEXT) \
	ta_test_func/ta_regtest-test_stoch.$(OBJEXT) \
	ta_test_func/ta_regtest-test_macd.$(OBJEXT) \
	ta_test_func/ta_regtest-test_minmax.$(OBJEXT) \
	ta_test_func/ta_regtest-test_per_hlcv.$(OBJEXT) \
	ta_test_func/ta_regtest-test_1in_1out.$(OBJEXT) \
	ta_test_func/ta_regtest-test_1in_2out.$(OBJEXT) \
	ta_test_func/ta_regtest-test_per_ohlc.$(OBJEXT) \
	ta_test_func/ta_regtest-test_stddev.$(OBJEXT) \
	ta_test_func/ta_regtest-test_bbands.$(OBJEXT) \
	ta_test_func/ta_regtest-test_ma.$(OBJEXT) \
	ta_test_func/ta_regtest-test_po.$(OBJEXT) \
	ta_test_func/ta_regtest-test_per_hl.$(OBJEXT) \
	ta_test_func/ta_regtest-test_trange.$(OBJEXT) \
	ta_test_func/ta_regtest-test_imi.$(OBJEXT) \
	ta_test_func/ta_regtest-test_avgdev.$(OBJEXT) \
	ta_regtest-test_internals.$(OBJEXT)
ta_regtest_OBJECTS = $(am_ta_regtest_OBJECTS)
am__DEPENDENCIES_1 =
ta_regtest_DEPENDENCIES = ../../libta-lib.la $(am__DEPENDENCIES_1)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
ta_regtest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(ta_regtest_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)/include
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/ta_regtest-ta_regtest.Po \
	./$(DEPDIR)/ta_regtest-test_abstract.Po \
	./$(DEPDIR)/ta_regtest-test_data.Po \
	./$(DEPDIR)/ta_regtest-test_internals.Po \
	./$(DEPDIR)/ta_regtest-test_util.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_po.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Po \
	ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(ta_regtest_SOURCES)
DIST_SOURCES = $(ta_regtest_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBM = @LIBM@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
POW_LIB = @POW_LIB@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TALIB_LIBRARY_VERSION = @TALIB_LIBRARY_VERSION@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
ta_regtest_SOURCES = ta_regtest.c \
	test_data.c \
	test_util.c \
	test_abstract.c \
	ta_test_func/test_adx.c \
	ta_test_func/test_mom.c \
	ta_test_func/test_sar.c \
	ta_test_func/test_rsi.c \
	ta_test_func/test_candlestick.c \
	ta_test_func/test_per_ema.c \
	ta_test_func/test_per_hlc.c \
	ta_test_func/test_stoch.c \
	ta_test_func/test_macd.c \
	ta_test_func/test_minmax.c \
	ta_test_func/test_per_hlcv.c \
	ta_test_func/test_1in_1out.c \
	ta_test_func/test_1in_2out.c \
	ta_test_func/test_per_ohlc.c \
	ta_test_func/test_stddev.c \
	ta_test_func/test_bbands.c \
	ta_test_func/test_ma.c \
	ta_test_func/test_po.c \
	ta_test_func/test_per_hl.c \
	ta_test_func/test_trange.c \
	ta_test_func/test_imi.c \
	ta_test_func/test_avgdev.c \
	test_internals.c

ta_regtest_CPPFLAGS = -I../../ta_func \
		      -I../../ta_common/trio \
		      -I../../ta_common/mt \
		      -I../../ta_common \
		      -I../../ta_abstract

ta_regtest_LDFLAGS = -no-undefined
ta_regtest_LDADD = ../../libta-lib.la $(LIBM)
all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign src/tools/ta_regtest/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign src/tools/ta_regtest/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list
ta_test_func/$(am__dirstamp):
	@$(MKDIR_P) ta_test_func
	@: > ta_test_func/$(am__dirstamp)
ta_test_func/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) ta_test_func/$(DEPDIR)
	@: > ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_adx.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_mom.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_sar.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_rsi.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_candlestick.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_per_ema.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_per_hlc.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_stoch.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_macd.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_minmax.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_per_hlcv.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_1in_1out.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_1in_2out.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_per_ohlc.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_stddev.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_bbands.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_ma.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_po.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_per_hl.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_trange.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_imi.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)
ta_test_func/ta_regtest-test_avgdev.$(OBJEXT):  \
	ta_test_func/$(am__dirstamp) \
	ta_test_func/$(DEPDIR)/$(am__dirstamp)

ta_regtest$(EXEEXT): $(ta_regtest_OBJECTS) $(ta_regtest_DEPENDENCIES) $(EXTRA_ta_regtest_DEPENDENCIES) 
	@rm -f ta_regtest$(EXEEXT)
	$(AM_V_CCLD)$(ta_regtest_LINK) $(ta_regtest_OBJECTS) $(ta_regtest_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f ta_test_func/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-ta_regtest.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_abstract.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_data.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_internals.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_util.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_po.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

ta_regtest-ta_regtest.o: ta_regtest.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-ta_regtest.o -MD -MP -MF $(DEPDIR)/ta_regtest-ta_regtest.Tpo -c -o ta_regtest-ta_regtest.o `test -f 'ta_regtest.c' || echo '$(srcdir)/'`ta_regtest.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-ta_regtest.Tpo $(DEPDIR)/ta_regtest-ta_regtest.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_regtest.c' object='ta_regtest-ta_regtest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-ta_regtest.o `test -f 'ta_regtest.c' || echo '$(srcdir)/'`ta_regtest.c

ta_regtest-ta_regtest.obj: ta_regtest.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-ta_regtest.obj -MD -MP -MF $(DEPDIR)/ta_regtest-ta_regtest.Tpo -c -o ta_regtest-ta_regtest.obj `if test -f 'ta_regtest.c'; then $(CYGPATH_W) 'ta_regtest.c'; else $(CYGPATH_W) '$(srcdir)/ta_regtest.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-ta_regtest.Tpo $(DEPDIR)/ta_regtest-ta_regtest.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_regtest.c' object='ta_regtest-ta_regtest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-ta_regtest.obj `if test -f 'ta_regtest.c'; then $(CYGPATH_W) 'ta_regtest.c'; else $(CYGPATH_W) '$(srcdir)/ta_regtest.c'; fi`

ta_regtest-test_data.o: test_data.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_data.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_data.Tpo -c -o ta_regtest-test_data.o `test -f 'test_data.c' || echo '$(srcdir)/'`test_data.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_data.Tpo $(DEPDIR)/ta_regtest-test_data.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_data.c' object='ta_regtest-test_data.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_data.o `test -f 'test_data.c' || echo '$(srcdir)/'`test_data.c

ta_regtest-test_data.obj: test_data.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_data.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_data.Tpo -c -o ta_regtest-test_data.obj `if test -f 'test_data.c'; then $(CYGPATH_W) 'test_data.c'; else $(CYGPATH_W) '$(srcdir)/test_data.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_data.Tpo $(DEPDIR)/ta_regtest-test_data.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_data.c' object='ta_regtest-test_data.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_data.obj `if test -f 'test_data.c'; then $(CYGPATH_W) 'test_data.c'; else $(CYGPATH_W) '$(srcdir)/test_data.c'; fi`

ta_regtest-test_util.o: test_util.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_util.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_util.Tpo -c -o ta_regtest-test_util.o `test -f 'test_util.c' || echo '$(srcdir)/'`test_util.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_util.Tpo $(DEPDIR)/ta_regtest-test_util.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_util.c' object='ta_regtest-test_util.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_util.o `test -f 'test_util.c' || echo '$(srcdir)/'`test_util.c

ta_regtest-test_util.obj: test_util.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_util.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_util.Tpo -c -o ta_regtest-test_util.obj `if test -f 'test_util.c'; then $(CYGPATH_W) 'test_util.c'; else $(CYGPATH_W) '$(srcdir)/test_util.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_util.Tpo $(DEPDIR)/ta_regtest-test_util.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_util.c' object='ta_regtest-test_util.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_util.obj `if test -f 'test_util.c'; then $(CYGPATH_W) 'test_util.c'; else $(CYGPATH_W) '$(srcdir)/test_util.c'; fi`

ta_regtest-test_abstract.o: test_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_abstract.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_abstract.Tpo -c -o ta_regtest-test_abstract.o `test -f 'test_abstract.c' || echo '$(srcdir)/'`test_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_abstract.Tpo $(DEPDIR)/ta_regtest-test_abstract.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_abstract.c' object='ta_regtest-test_abstract.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_abstract.o `test -f 'test_abstract.c' || echo '$(srcdir)/'`test_abstract.c

ta_regtest-test_abstract.obj: test_abstract.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_abstract.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_abstract.Tpo -c -o ta_regtest-test_abstract.obj `if test -f 'test_abstract.c'; then $(CYGPATH_W) 'test_abstract.c'; else $(CYGPATH_W) '$(srcdir)/test_abstract.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_abstract.Tpo $(DEPDIR)/ta_regtest-test_abstract.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_abstract.c' object='ta_regtest-test_abstract.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_abstract.obj `if test -f 'test_abstract.c'; then $(CYGPATH_W) 'test_abstract.c'; else $(CYGPATH_W) '$(srcdir)/test_abstract.c'; fi`

ta_test_func/ta_regtest-test_adx.o: ta_test_func/test_adx.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_adx.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Tpo -c -o ta_test_func/ta_regtest-test_adx.o `test -f 'ta_test_func/test_adx.c' || echo '$(srcdir)/'`ta_test_func/test_adx.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_adx.c' object='ta_test_func/ta_regtest-test_adx.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_adx.o `test -f 'ta_test_func/test_adx.c' || echo '$(srcdir)/'`ta_test_func/test_adx.c

ta_test_func/ta_regtest-test_adx.obj: ta_test_func/test_adx.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_adx.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Tpo -c -o ta_test_func/ta_regtest-test_adx.obj `if test -f 'ta_test_func/test_adx.c'; then $(CYGPATH_W) 'ta_test_func/test_adx.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_adx.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_adx.c' object='ta_test_func/ta_regtest-test_adx.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_adx.obj `if test -f 'ta_test_func/test_adx.c'; then $(CYGPATH_W) 'ta_test_func/test_adx.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_adx.c'; fi`

ta_test_func/ta_regtest-test_mom.o: ta_test_func/test_mom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_mom.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Tpo -c -o ta_test_func/ta_regtest-test_mom.o `test -f 'ta_test_func/test_mom.c' || echo '$(srcdir)/'`ta_test_func/test_mom.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_mom.c' object='ta_test_func/ta_regtest-test_mom.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_mom.o `test -f 'ta_test_func/test_mom.c' || echo '$(srcdir)/'`ta_test_func/test_mom.c

ta_test_func/ta_regtest-test_mom.obj: ta_test_func/test_mom.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_mom.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Tpo -c -o ta_test_func/ta_regtest-test_mom.obj `if test -f 'ta_test_func/test_mom.c'; then $(CYGPATH_W) 'ta_test_func/test_mom.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_mom.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_mom.c' object='ta_test_func/ta_regtest-test_mom.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_mom.obj `if test -f 'ta_test_func/test_mom.c'; then $(CYGPATH_W) 'ta_test_func/test_mom.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_mom.c'; fi`

ta_test_func/ta_regtest-test_sar.o: ta_test_func/test_sar.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_sar.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Tpo -c -o ta_test_func/ta_regtest-test_sar.o `test -f 'ta_test_func/test_sar.c' || echo '$(srcdir)/'`ta_test_func/test_sar.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_sar.c' object='ta_test_func/ta_regtest-test_sar.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_sar.o `test -f 'ta_test_func/test_sar.c' || echo '$(srcdir)/'`ta_test_func/test_sar.c

ta_test_func/ta_regtest-test_sar.obj: ta_test_func/test_sar.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_sar.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Tpo -c -o ta_test_func/ta_regtest-test_sar.obj `if test -f 'ta_test_func/test_sar.c'; then $(CYGPATH_W) 'ta_test_func/test_sar.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_sar.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_sar.c' object='ta_test_func/ta_regtest-test_sar.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_sar.obj `if test -f 'ta_test_func/test_sar.c'; then $(CYGPATH_W) 'ta_test_func/test_sar.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_sar.c'; fi`

ta_test_func/ta_regtest-test_rsi.o: ta_test_func/test_rsi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_rsi.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Tpo -c -o ta_test_func/ta_regtest-test_rsi.o `test -f 'ta_test_func/test_rsi.c' || echo '$(srcdir)/'`ta_test_func/test_rsi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_rsi.c' object='ta_test_func/ta_regtest-test_rsi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_rsi.o `test -f 'ta_test_func/test_rsi.c' || echo '$(srcdir)/'`ta_test_func/test_rsi.c

ta_test_func/ta_regtest-test_rsi.obj: ta_test_func/test_rsi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_rsi.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Tpo -c -o ta_test_func/ta_regtest-test_rsi.obj `if test -f 'ta_test_func/test_rsi.c'; then $(CYGPATH_W) 'ta_test_func/test_rsi.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_rsi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_rsi.c' object='ta_test_func/ta_regtest-test_rsi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_rsi.obj `if test -f 'ta_test_func/test_rsi.c'; then $(CYGPATH_W) 'ta_test_func/test_rsi.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_rsi.c'; fi`

ta_test_func/ta_regtest-test_candlestick.o: ta_test_func/test_candlestick.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_candlestick.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Tpo -c -o ta_test_func/ta_regtest-test_candlestick.o `test -f 'ta_test_func/test_candlestick.c' || echo '$(srcdir)/'`ta_test_func/test_candlestick.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_candlestick.c' object='ta_test_func/ta_regtest-test_candlestick.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_candlestick.o `test -f 'ta_test_func/test_candlestick.c' || echo '$(srcdir)/'`ta_test_func/test_candlestick.c

ta_test_func/ta_regtest-test_candlestick.obj: ta_test_func/test_candlestick.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_candlestick.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Tpo -c -o ta_test_func/ta_regtest-test_candlestick.obj `if test -f 'ta_test_func/test_candlestick.c'; then $(CYGPATH_W) 'ta_test_func/test_candlestick.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_candlestick.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_candlestick.c' object='ta_test_func/ta_regtest-test_candlestick.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_candlestick.obj `if test -f 'ta_test_func/test_candlestick.c'; then $(CYGPATH_W) 'ta_test_func/test_candlestick.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_candlestick.c'; fi`

ta_test_func/ta_regtest-test_per_ema.o: ta_test_func/test_per_ema.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_ema.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Tpo -c -o ta_test_func/ta_regtest-test_per_ema.o `test -f 'ta_test_func/test_per_ema.c' || echo '$(srcdir)/'`ta_test_func/test_per_ema.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_ema.c' object='ta_test_func/ta_regtest-test_per_ema.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_ema.o `test -f 'ta_test_func/test_per_ema.c' || echo '$(srcdir)/'`ta_test_func/test_per_ema.c

ta_test_func/ta_regtest-test_per_ema.obj: ta_test_func/test_per_ema.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_ema.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Tpo -c -o ta_test_func/ta_regtest-test_per_ema.obj `if test -f 'ta_test_func/test_per_ema.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ema.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ema.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_ema.c' object='ta_test_func/ta_regtest-test_per_ema.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_ema.obj `if test -f 'ta_test_func/test_per_ema.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ema.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ema.c'; fi`

ta_test_func/ta_regtest-test_per_hlc.o: ta_test_func/test_per_hlc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_hlc.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Tpo -c -o ta_test_func/ta_regtest-test_per_hlc.o `test -f 'ta_test_func/test_per_hlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlc.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_hlc.c' object='ta_test_func/ta_regtest-test_per_hlc.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_hlc.o `test -f 'ta_test_func/test_per_hlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlc.c

ta_test_func/ta_regtest-test_per_hlc.obj: ta_test_func/test_per_hlc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_hlc.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Tpo -c -o ta_test_func/ta_regtest-test_per_hlc.obj `if test -f 'ta_test_func/test_per_hlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlc.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_hlc.c' object='ta_test_func/ta_regtest-test_per_hlc.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_hlc.obj `if test -f 'ta_test_func/test_per_hlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlc.c'; fi`

ta_test_func/ta_regtest-test_stoch.o: ta_test_func/test_stoch.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_stoch.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Tpo -c -o ta_test_func/ta_regtest-test_stoch.o `test -f 'ta_test_func/test_stoch.c' || echo '$(srcdir)/'`ta_test_func/test_stoch.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_stoch.c' object='ta_test_func/ta_regtest-test_stoch.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_stoch.o `test -f 'ta_test_func/test_stoch.c' || echo '$(srcdir)/'`ta_test_func/test_stoch.c

ta_test_func/ta_regtest-test_stoch.obj: ta_test_func/test_stoch.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_stoch.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Tpo -c -o ta_test_func/ta_regtest-test_stoch.obj `if test -f 'ta_test_func/test_stoch.c'; then $(CYGPATH_W) 'ta_test_func/test_stoch.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stoch.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_stoch.c' object='ta_test_func/ta_regtest-test_stoch.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_stoch.obj `if test -f 'ta_test_func/test_stoch.c'; then $(CYGPATH_W) 'ta_test_func/test_stoch.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stoch.c'; fi`

ta_test_func/ta_regtest-test_macd.o: ta_test_func/test_macd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_macd.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Tpo -c -o ta_test_func/ta_regtest-test_macd.o `test -f 'ta_test_func/test_macd.c' || echo '$(srcdir)/'`ta_test_func/test_macd.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_macd.c' object='ta_test_func/ta_regtest-test_macd.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_macd.o `test -f 'ta_test_func/test_macd.c' || echo '$(srcdir)/'`ta_test_func/test_macd.c

ta_test_func/ta_regtest-test_macd.obj: ta_test_func/test_macd.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_macd.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Tpo -c -o ta_test_func/ta_regtest-test_macd.obj `if test -f 'ta_test_func/test_macd.c'; then $(CYGPATH_W) 'ta_test_func/test_macd.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_macd.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_macd.c' object='ta_test_func/ta_regtest-test_macd.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_macd.obj `if test -f 'ta_test_func/test_macd.c'; then $(CYGPATH_W) 'ta_test_func/test_macd.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_macd.c'; fi`

ta_test_func/ta_regtest-test_minmax.o: ta_test_func/test_minmax.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_minmax.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Tpo -c -o ta_test_func/ta_regtest-test_minmax.o `test -f 'ta_test_func/test_minmax.c' || echo '$(srcdir)/'`ta_test_func/test_minmax.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_minmax.c' object='ta_test_func/ta_regtest-test_minmax.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_minmax.o `test -f 'ta_test_func/test_minmax.c' || echo '$(srcdir)/'`ta_test_func/test_minmax.c

ta_test_func/ta_regtest-test_minmax.obj: ta_test_func/test_minmax.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_minmax.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Tpo -c -o ta_test_func/ta_regtest-test_minmax.obj `if test -f 'ta_test_func/test_minmax.c'; then $(CYGPATH_W) 'ta_test_func/test_minmax.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_minmax.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_minmax.c' object='ta_test_func/ta_regtest-test_minmax.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_minmax.obj `if test -f 'ta_test_func/test_minmax.c'; then $(CYGPATH_W) 'ta_test_func/test_minmax.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_minmax.c'; fi`

ta_test_func/ta_regtest-test_per_hlcv.o: ta_test_func/test_per_hlcv.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_hlcv.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Tpo -c -o ta_test_func/ta_regtest-test_per_hlcv.o `test -f 'ta_test_func/test_per_hlcv.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlcv.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_hlcv.c' object='ta_test_func/ta_regtest-test_per_hlcv.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_hlcv.o `test -f 'ta_test_func/test_per_hlcv.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlcv.c

ta_test_func/ta_regtest-test_per_hlcv.obj: ta_test_func/test_per_hlcv.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_hlcv.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Tpo -c -o ta_test_func/ta_regtest-test_per_hlcv.obj `if test -f 'ta_test_func/test_per_hlcv.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlcv.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlcv.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_hlcv.c' object='ta_test_func/ta_regtest-test_per_hlcv.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_hlcv.obj `if test -f 'ta_test_func/test_per_hlcv.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlcv.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlcv.c'; fi`

ta_test_func/ta_regtest-test_1in_1out.o: ta_test_func/test_1in_1out.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_1in_1out.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Tpo -c -o ta_test_func/ta_regtest-test_1in_1out.o `test -f 'ta_test_func/test_1in_1out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_1out.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_1in_1out.c' object='ta_test_func/ta_regtest-test_1in_1out.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_1in_1out.o `test -f 'ta_test_func/test_1in_1out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_1out.c

ta_test_func/ta_regtest-test_1in_1out.obj: ta_test_func/test_1in_1out.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_1in_1out.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Tpo -c -o ta_test_func/ta_regtest-test_1in_1out.obj `if test -f 'ta_test_func/test_1in_1out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_1out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_1out.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_1in_1out.c' object='ta_test_func/ta_regtest-test_1in_1out.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_1in_1out.obj `if test -f 'ta_test_func/test_1in_1out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_1out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_1out.c'; fi`

ta_test_func/ta_regtest-test_1in_2out.o: ta_test_func/test_1in_2out.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_1in_2out.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Tpo -c -o ta_test_func/ta_regtest-test_1in_2out.o `test -f 'ta_test_func/test_1in_2out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_2out.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_1in_2out.c' object='ta_test_func/ta_regtest-test_1in_2out.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_1in_2out.o `test -f 'ta_test_func/test_1in_2out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_2out.c

ta_test_func/ta_regtest-test_1in_2out.obj: ta_test_func/test_1in_2out.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_1in_2out.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Tpo -c -o ta_test_func/ta_regtest-test_1in_2out.obj `if test -f 'ta_test_func/test_1in_2out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_2out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_2out.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_1in_2out.c' object='ta_test_func/ta_regtest-test_1in_2out.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_1in_2out.obj `if test -f 'ta_test_func/test_1in_2out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_2out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_2out.c'; fi`

ta_test_func/ta_regtest-test_per_ohlc.o: ta_test_func/test_per_ohlc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_ohlc.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Tpo -c -o ta_test_func/ta_regtest-test_per_ohlc.o `test -f 'ta_test_func/test_per_ohlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_ohlc.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_ohlc.c' object='ta_test_func/ta_regtest-test_per_ohlc.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_ohlc.o `test -f 'ta_test_func/test_per_ohlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_ohlc.c

ta_test_func/ta_regtest-test_per_ohlc.obj: ta_test_func/test_per_ohlc.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_ohlc.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Tpo -c -o ta_test_func/ta_regtest-test_per_ohlc.obj `if test -f 'ta_test_func/test_per_ohlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ohlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ohlc.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_ohlc.c' object='ta_test_func/ta_regtest-test_per_ohlc.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_ohlc.obj `if test -f 'ta_test_func/test_per_ohlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ohlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ohlc.c'; fi`

ta_test_func/ta_regtest-test_stddev.o: ta_test_func/test_stddev.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_stddev.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Tpo -c -o ta_test_func/ta_regtest-test_stddev.o `test -f 'ta_test_func/test_stddev.c' || echo '$(srcdir)/'`ta_test_func/test_stddev.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_stddev.c' object='ta_test_func/ta_regtest-test_stddev.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_stddev.o `test -f 'ta_test_func/test_stddev.c' || echo '$(srcdir)/'`ta_test_func/test_stddev.c

ta_test_func/ta_regtest-test_stddev.obj: ta_test_func/test_stddev.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_stddev.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Tpo -c -o ta_test_func/ta_regtest-test_stddev.obj `if test -f 'ta_test_func/test_stddev.c'; then $(CYGPATH_W) 'ta_test_func/test_stddev.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stddev.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_stddev.c' object='ta_test_func/ta_regtest-test_stddev.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_stddev.obj `if test -f 'ta_test_func/test_stddev.c'; then $(CYGPATH_W) 'ta_test_func/test_stddev.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stddev.c'; fi`

ta_test_func/ta_regtest-test_bbands.o: ta_test_func/test_bbands.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_bbands.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Tpo -c -o ta_test_func/ta_regtest-test_bbands.o `test -f 'ta_test_func/test_bbands.c' || echo '$(srcdir)/'`ta_test_func/test_bbands.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_bbands.c' object='ta_test_func/ta_regtest-test_bbands.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_bbands.o `test -f 'ta_test_func/test_bbands.c' || echo '$(srcdir)/'`ta_test_func/test_bbands.c

ta_test_func/ta_regtest-test_bbands.obj: ta_test_func/test_bbands.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_bbands.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Tpo -c -o ta_test_func/ta_regtest-test_bbands.obj `if test -f 'ta_test_func/test_bbands.c'; then $(CYGPATH_W) 'ta_test_func/test_bbands.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_bbands.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_bbands.c' object='ta_test_func/ta_regtest-test_bbands.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_bbands.obj `if test -f 'ta_test_func/test_bbands.c'; then $(CYGPATH_W) 'ta_test_func/test_bbands.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_bbands.c'; fi`

ta_test_func/ta_regtest-test_ma.o: ta_test_func/test_ma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_ma.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Tpo -c -o ta_test_func/ta_regtest-test_ma.o `test -f 'ta_test_func/test_ma.c' || echo '$(srcdir)/'`ta_test_func/test_ma.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_ma.c' object='ta_test_func/ta_regtest-test_ma.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_ma.o `test -f 'ta_test_func/test_ma.c' || echo '$(srcdir)/'`ta_test_func/test_ma.c

ta_test_func/ta_regtest-test_ma.obj: ta_test_func/test_ma.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_ma.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Tpo -c -o ta_test_func/ta_regtest-test_ma.obj `if test -f 'ta_test_func/test_ma.c'; then $(CYGPATH_W) 'ta_test_func/test_ma.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_ma.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_ma.c' object='ta_test_func/ta_regtest-test_ma.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_ma.obj `if test -f 'ta_test_func/test_ma.c'; then $(CYGPATH_W) 'ta_test_func/test_ma.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_ma.c'; fi`

ta_test_func/ta_regtest-test_po.o: ta_test_func/test_po.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_po.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_po.Tpo -c -o ta_test_func/ta_regtest-test_po.o `test -f 'ta_test_func/test_po.c' || echo '$(srcdir)/'`ta_test_func/test_po.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_po.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_po.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_po.c' object='ta_test_func/ta_regtest-test_po.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_po.o `test -f 'ta_test_func/test_po.c' || echo '$(srcdir)/'`ta_test_func/test_po.c

ta_test_func/ta_regtest-test_po.obj: ta_test_func/test_po.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_po.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_po.Tpo -c -o ta_test_func/ta_regtest-test_po.obj `if test -f 'ta_test_func/test_po.c'; then $(CYGPATH_W) 'ta_test_func/test_po.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_po.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_po.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_po.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_po.c' object='ta_test_func/ta_regtest-test_po.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_po.obj `if test -f 'ta_test_func/test_po.c'; then $(CYGPATH_W) 'ta_test_func/test_po.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_po.c'; fi`

ta_test_func/ta_regtest-test_per_hl.o: ta_test_func/test_per_hl.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_hl.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Tpo -c -o ta_test_func/ta_regtest-test_per_hl.o `test -f 'ta_test_func/test_per_hl.c' || echo '$(srcdir)/'`ta_test_func/test_per_hl.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_hl.c' object='ta_test_func/ta_regtest-test_per_hl.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_hl.o `test -f 'ta_test_func/test_per_hl.c' || echo '$(srcdir)/'`ta_test_func/test_per_hl.c

ta_test_func/ta_regtest-test_per_hl.obj: ta_test_func/test_per_hl.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_per_hl.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Tpo -c -o ta_test_func/ta_regtest-test_per_hl.obj `if test -f 'ta_test_func/test_per_hl.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hl.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hl.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_per_hl.c' object='ta_test_func/ta_regtest-test_per_hl.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_per_hl.obj `if test -f 'ta_test_func/test_per_hl.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hl.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hl.c'; fi`

ta_test_func/ta_regtest-test_trange.o: ta_test_func/test_trange.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_trange.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Tpo -c -o ta_test_func/ta_regtest-test_trange.o `test -f 'ta_test_func/test_trange.c' || echo '$(srcdir)/'`ta_test_func/test_trange.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_trange.c' object='ta_test_func/ta_regtest-test_trange.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_trange.o `test -f 'ta_test_func/test_trange.c' || echo '$(srcdir)/'`ta_test_func/test_trange.c

ta_test_func/ta_regtest-test_trange.obj: ta_test_func/test_trange.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_trange.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Tpo -c -o ta_test_func/ta_regtest-test_trange.obj `if test -f 'ta_test_func/test_trange.c'; then $(CYGPATH_W) 'ta_test_func/test_trange.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_trange.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_trange.c' object='ta_test_func/ta_regtest-test_trange.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_trange.obj `if test -f 'ta_test_func/test_trange.c'; then $(CYGPATH_W) 'ta_test_func/test_trange.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_trange.c'; fi`

ta_test_func/ta_regtest-test_imi.o: ta_test_func/test_imi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_imi.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Tpo -c -o ta_test_func/ta_regtest-test_imi.o `test -f 'ta_test_func/test_imi.c' || echo '$(srcdir)/'`ta_test_func/test_imi.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_imi.c' object='ta_test_func/ta_regtest-test_imi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_imi.o `test -f 'ta_test_func/test_imi.c' || echo '$(srcdir)/'`ta_test_func/test_imi.c

ta_test_func/ta_regtest-test_imi.obj: ta_test_func/test_imi.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_imi.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Tpo -c -o ta_test_func/ta_regtest-test_imi.obj `if test -f 'ta_test_func/test_imi.c'; then $(CYGPATH_W) 'ta_test_func/test_imi.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_imi.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_imi.c' object='ta_test_func/ta_regtest-test_imi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_imi.obj `if test -f 'ta_test_func/test_imi.c'; then $(CYGPATH_W) 'ta_test_func/test_imi.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_imi.c'; fi`

ta_test_func/ta_regtest-test_avgdev.o: ta_test_func/test_avgdev.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_avgdev.o -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Tpo -c -o ta_test_func/ta_regtest-test_avgdev.o `test -f 'ta_test_func/test_avgdev.c' || echo '$(srcdir)/'`ta_test_func/test_avgdev.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_avgdev.c' object='ta_test_func/ta_regtest-test_avgdev.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_avgdev.o `test -f 'ta_test_func/test_avgdev.c' || echo '$(srcdir)/'`ta_test_func/test_avgdev.c

ta_test_func/ta_regtest-test_avgdev.obj: ta_test_func/test_avgdev.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_test_func/ta_regtest-test_avgdev.obj -MD -MP -MF ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Tpo -c -o ta_test_func/ta_regtest-test_avgdev.obj `if test -f 'ta_test_func/test_avgdev.c'; then $(CYGPATH_W) 'ta_test_func/test_avgdev.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_avgdev.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Tpo ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='ta_test_func/test_avgdev.c' object='ta_test_func/ta_regtest-test_avgdev.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_test_func/ta_regtest-test_avgdev.obj `if test -f 'ta_test_func/test_avgdev.c'; then $(CYGPATH_W) 'ta_test_func/test_avgdev.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_avgdev.c'; fi`

ta_regtest-test_internals.o: test_internals.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_internals.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_internals.Tpo -c -o ta_regtest-test_internals.o `test -f 'test_internals.c' || echo '$(srcdir)/'`test_internals.c
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_internals.Tpo $(DEPDIR)/ta_regtest-test_internals.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_internals.c' object='ta_regtest-test_internals.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_internals.o `test -f 'test_internals.c' || echo '$(srcdir)/'`test_internals.c

ta_regtest-test_internals.obj: test_internals.c
@am__fastdepCC_TRUE@	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_internals.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_internals.Tpo -c -o ta_regtest-test_internals.obj `if test -f 'test_internals.c'; then $(CYGPATH_W) 'test_internals.c'; else $(CYGPATH_W) '$(srcdir)/test_internals.c'; fi`
@am__fastdepCC_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/ta_regtest-test_internals.Tpo $(DEPDIR)/ta_regtest-test_internals.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='test_internals.c' object='ta_regtest-test_internals.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_internals.obj `if test -f 'test_internals.c'; then $(CYGPATH_W) 'test_internals.c'; else $(CYGPATH_W) '$(srcdir)/test_internals.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS) all-local
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f ta_test_func/$(DEPDIR)/$(am__dirstamp)
	-rm -f ta_test_func/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/ta_regtest-ta_regtest.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_abstract.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_data.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_internals.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_util.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_po.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/ta_regtest-ta_regtest.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_abstract.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_data.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_internals.Po
	-rm -f ./$(DEPDIR)/ta_regtest-test_util.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_1in_1out.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_1in_2out.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_adx.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_avgdev.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_bbands.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_candlestick.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_imi.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_ma.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_macd.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_minmax.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_mom.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_ema.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_hl.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlc.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_hlcv.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_per_ohlc.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_po.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_rsi.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_sar.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_stddev.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_stoch.Po
	-rm -f ta_test_func/$(DEPDIR)/ta_regtest-test_trange.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am all-local am--depfiles check \
	check-am clean clean-generic clean-libtool \
	clean-noinstPROGRAMS cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am

.PRECIOUS: Makefile


all-local: ta_regtest
	$(LIBTOOL) --mode=execute ../post-build-bin.sh ta_regtest

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
